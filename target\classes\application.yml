server:
  port: 9091  # 综合办公系统端口
  ssl:
    enabled: false  # 先使用HTTP，后续可配置HTTPS

spring:
  application:
    name: mock-service

# 挡板服务配置
mock:
  # 综合办公系统配置
  zhbg:
    port: 9091
    enabled: true
  
  # WebService配置
  webservice:
    port: 8443
    enabled: true
    context-path: /xmysfzjjg/wservices
  
  # 新中平
  socket:
    port: 9702
    enabled: true

# CXF配置
cxf:
  path: /xmysfzjjg/wservices
  jaxws:
    servlet:
      load-on-startup: 1

# 日志配置
logging:
  level:
    com.psbc.mock: INFO
    org.apache.cxf: INFO
    io.netty: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
