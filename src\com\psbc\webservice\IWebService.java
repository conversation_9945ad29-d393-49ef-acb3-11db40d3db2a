package com.psbc.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import org.springframework.stereotype.Service;

@Service
@WebService
public interface IWebService {
  @WebMethod
  @WebResult(targetNamespace = "http://webservice.psbc.com/")
  String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/") String paramString);
  
  
  @WebMethod
  @WebResult(targetNamespace = "http://webservice.psbc.com/")
  String Execute(@WebParam(targetNamespace = "http://webservice.psbc.com/") String paramString1, String paramString2);
}
