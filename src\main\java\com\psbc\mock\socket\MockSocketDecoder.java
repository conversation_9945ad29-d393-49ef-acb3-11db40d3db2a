package com.psbc.mock.socket;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 外联系统Socket消息解码器
 * 解析6位长度前缀的消息格式
 */
@Slf4j
public class MockSocketDecoder extends ByteToMessageDecoder {

    private static final int LENGTH_FIELD_SIZE = 6;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 检查是否有足够的字节来读取长度字段
        if (in.readableBytes() < LENGTH_FIELD_SIZE) {
            return;
        }

        // 标记当前读取位置
        in.markReaderIndex();

        // 读取长度字段（6字节）
        byte[] lengthBytes = new byte[LENGTH_FIELD_SIZE];
        in.readBytes(lengthBytes);
        String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
        
        int messageLength;
        try {
            messageLength = Integer.parseInt(lengthStr);
            log.debug("解析到消息长度: {}", messageLength);
        } catch (NumberFormatException e) {
            log.error("无法解析长度字段: {}", lengthStr);
            // 重置读取位置并跳过这个字节
            in.resetReaderIndex();
            in.readByte();
            return;
        }

        // 检查是否有足够的字节来读取完整的消息体
        if (in.readableBytes() < messageLength) {
            // 重置读取位置，等待更多数据
            in.resetReaderIndex();
            return;
        }

        // 读取消息体
        byte[] messageBytes = new byte[messageLength];
        in.readBytes(messageBytes);
        String message = new String(messageBytes, StandardCharsets.UTF_8);
        
        log.info("外联系统收到消息: {}", message);
        out.add(message);
    }
}
