package com.psbc.mock.client;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * 挡板服务测试客户端
 * 用于验证挡板服务的各个接口是否正常工作
 */
@Slf4j
public class MockServiceTestClient {

    private static final String BASE_URL = "http://localhost:9091";
    private static final String SOCKET_HOST = "localhost";
    private static final int SOCKET_PORT = 9702;

    public static void main(String[] args) {
        MockServiceTestClient client = new MockServiceTestClient();
        
        log.info("开始测试挡板服务...");
        
        // 等待服务启动
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试综合办公系统接口
        client.test30001Interface();
        client.test20006Interface();
        client.testHealthCheck();
        
        // 测试Socket接口
        client.testSocketInterface();
        
        log.info("挡板服务测试完成");
    }

    /**
     * 测试30001接口
     */
    public void test30001Interface() {
        try {
            log.info("测试30001接口...");
            String url = BASE_URL + "/api/admin/xzp/queryYxjfLsxdye";
            String requestBody = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
            
            String response = sendHttpRequest(url, requestBody, "application/json");
            log.info("30001接口响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试30001接口失败", e);
        }
    }

    /**
     * 测试20006接口
     */
    public void test20006Interface() {
        try {
            log.info("测试20006接口...");
            String url = BASE_URL + "/api/admin/xzp/queryJgzhInfo";
            String requestBody = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>20006</serviceno></head>" +
                    "<body><data>test xml message</data></body></content>";
            
            String response = sendHttpRequest(url, requestBody, "application/xml");
            log.info("20006接口响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试20006接口失败", e);
        }
    }

    /**
     * 测试健康检查接口
     */
    public void testHealthCheck() {
        try {
            log.info("测试健康检查接口...");
            String url = BASE_URL + "/api/admin/xzp/health";
            
            String response = sendGetRequest(url);
            log.info("健康检查响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试健康检查接口失败", e);
        }
    }

    /**
     * 测试Socket接口
     */
    public void testSocketInterface() {
        try {
            log.info("测试Socket接口...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>99999</serviceno></head>" +
                    "<body><data>test socket message</data></body></content>";
            
            String response = sendSocketMessage(message);
            log.info("Socket接口响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试Socket接口失败", e);
        }
    }

    /**
     * 发送HTTP POST请求
     */
    private String sendHttpRequest(String urlStr, String requestBody, String contentType) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", contentType);
        conn.connect();
        
        // 发送请求体
        try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8)) {
            out.write(requestBody);
            out.flush();
        }
        
        // 读取响应
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        
        return sb.toString();
    }

    /**
     * 发送HTTP GET请求
     */
    private String sendGetRequest(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setRequestMethod("GET");
        conn.connect();
        
        // 读取响应
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        
        return sb.toString();
    }

    /**
     * 发送Socket消息
     */
    private String sendSocketMessage(String message) throws Exception {
        try (Socket socket = new Socket(SOCKET_HOST, SOCKET_PORT)) {
            // 发送消息
            sendFormattedMessage(socket, message);
            
            // 接收响应
            return receiveFormattedMessage(socket);
        }
    }

    /**
     * 发送格式化的消息（添加6位长度前缀）
     */
    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        // 格式化长度字段为6位字符串
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        try (OutputStream out = socket.getOutputStream()) {
            // 发送长度字段
            out.write(lengthBytes);
            // 发送消息体
            out.write(messageBytes);
            out.flush();
        }
    }

    /**
     * 接收格式化的消息（解析6位长度前缀）
     */
    private String receiveFormattedMessage(Socket socket) throws IOException {
        try (InputStream in = socket.getInputStream()) {
            // 读取长度字段（6字节）
            byte[] lengthBytes = new byte[6];
            int bytesRead = in.read(lengthBytes);
            if (bytesRead != 6) {
                throw new IOException("无法读取完整的长度字段");
            }
            
            String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
            int messageLength = Integer.parseInt(lengthStr);
            
            // 读取消息体
            byte[] messageBytes = new byte[messageLength];
            int totalBytesRead = 0;
            while (totalBytesRead < messageLength) {
                int currentBytesRead = in.read(messageBytes, totalBytesRead, messageLength - totalBytesRead);
                if (currentBytesRead == -1) {
                    throw new IOException("连接意外关闭");
                }
                totalBytesRead += currentBytesRead;
            }
            
            return new String(messageBytes, StandardCharsets.UTF_8);
        }
    }
}
