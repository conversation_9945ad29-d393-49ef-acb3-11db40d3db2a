@echo off
echo ========================================
echo xmysfzjjg WAR包构建脚本
echo ========================================

cd /d "%~dp0"

echo.
echo 选择构建方式：
echo 1. 使用 Ant 构建
echo 2. 使用 Maven 构建
echo 3. 手动构建（使用jar命令）
echo.
set /p choice=请选择 (1-3): 

if "%choice%"=="1" goto ant_build
if "%choice%"=="2" goto maven_build
if "%choice%"=="3" goto manual_build
echo 无效选择，退出...
goto end

:ant_build
echo.
echo ========================================
echo 使用 Ant 构建 WAR 包
echo ========================================
echo.

REM 检查Ant是否可用
ant -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 Ant，请确保 Ant 已安装并在 PATH 中
    goto end
)

echo 开始 Ant 构建...
ant clean compile war

if %errorlevel% == 0 (
    echo.
    echo ✓ Ant 构建成功！
    echo WAR文件位置: dist\xmysfzjjg.war
) else (
    echo.
    echo ✗ Ant 构建失败！
)
goto end

:maven_build
echo.
echo ========================================
echo 使用 Maven 构建 WAR 包
echo ========================================
echo.

REM 检查Maven是否可用
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 Maven，请确保 Maven 已安装并在 PATH 中
    goto end
)

echo 开始 Maven 构建...
mvn clean compile package

if %errorlevel% == 0 (
    echo.
    echo ✓ Maven 构建成功！
    echo WAR文件位置: target\xmysfzjjg.war
) else (
    echo.
    echo ✗ Maven 构建失败！
)
goto end

:manual_build
echo.
echo ========================================
echo 手动构建 WAR 包
echo ========================================
echo.

REM 检查Java是否可用
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 Java，请确保 Java 已安装并在 PATH 中
    goto end
)

echo 开始手动构建...

REM 创建临时目录
if exist temp_war rmdir /s /q temp_war
mkdir temp_war

echo 1. 复制 WebContent 内容...
xcopy /s /e /q WebContent temp_war\

echo 2. 复制编译后的类文件...
if exist build\classes (
    xcopy /s /e /q build\classes temp_war\WEB-INF\classes\
) else (
    echo 警告：未找到编译后的类文件，请先编译项目
)

echo 3. 创建 WAR 文件...
cd temp_war
jar -cvf ..\xmysfzjjg.war *
cd ..

echo 4. 清理临时文件...
rmdir /s /q temp_war

if exist xmysfzjjg.war (
    echo.
    echo ✓ 手动构建成功！
    echo WAR文件位置: xmysfzjjg.war
) else (
    echo.
    echo ✗ 手动构建失败！
)
goto end

:end
echo.
echo ========================================
echo 构建完成
echo ========================================
echo.
echo 部署说明：
echo 1. 将生成的 WAR 文件复制到 Tomcat 的 webapps 目录
echo 2. 启动 Tomcat 服务器
echo 3. 访问 http://localhost:8080/xmysfzjjg/
echo 4. WebService 地址: http://localhost:8080/xmysfzjjg/wservices/
echo.
pause
