#!/bin/bash

echo "========================================"
echo "xmysfzjjg WAR包构建脚本"
echo "========================================"

cd "$(dirname "$0")"

echo ""
echo "选择构建方式："
echo "1. 使用 Ant 构建"
echo "2. 使用 Maven 构建"
echo "3. 手动构建（使用jar命令）"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "========================================"
        echo "使用 Ant 构建 WAR 包"
        echo "========================================"
        echo ""
        
        # 检查Ant是否可用
        if ! command -v ant &> /dev/null; then
            echo "错误：未找到 Ant，请确保 Ant 已安装并在 PATH 中"
            exit 1
        fi
        
        echo "开始 Ant 构建..."
        ant clean compile war
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "✓ Ant 构建成功！"
            echo "WAR文件位置: dist/xmysfzjjg.war"
        else
            echo ""
            echo "✗ Ant 构建失败！"
        fi
        ;;
        
    2)
        echo ""
        echo "========================================"
        echo "使用 Maven 构建 WAR 包"
        echo "========================================"
        echo ""
        
        # 检查Maven是否可用
        if ! command -v mvn &> /dev/null; then
            echo "错误：未找到 Maven，请确保 Maven 已安装并在 PATH 中"
            exit 1
        fi
        
        echo "开始 Maven 构建..."
        mvn clean compile package
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "✓ Maven 构建成功！"
            echo "WAR文件位置: target/xmysfzjjg.war"
        else
            echo ""
            echo "✗ Maven 构建失败！"
        fi
        ;;
        
    3)
        echo ""
        echo "========================================"
        echo "手动构建 WAR 包"
        echo "========================================"
        echo ""
        
        # 检查Java是否可用
        if ! command -v java &> /dev/null; then
            echo "错误：未找到 Java，请确保 Java 已安装并在 PATH 中"
            exit 1
        fi
        
        echo "开始手动构建..."
        
        # 创建临时目录
        rm -rf temp_war
        mkdir temp_war
        
        echo "1. 复制 WebContent 内容..."
        cp -r WebContent/* temp_war/
        
        echo "2. 复制编译后的类文件..."
        if [ -d "build/classes" ]; then
            mkdir -p temp_war/WEB-INF/classes
            cp -r build/classes/* temp_war/WEB-INF/classes/
        else
            echo "警告：未找到编译后的类文件，请先编译项目"
        fi
        
        echo "3. 创建 WAR 文件..."
        cd temp_war
        jar -cvf ../xmysfzjjg.war *
        cd ..
        
        echo "4. 清理临时文件..."
        rm -rf temp_war
        
        if [ -f "xmysfzjjg.war" ]; then
            echo ""
            echo "✓ 手动构建成功！"
            echo "WAR文件位置: xmysfzjjg.war"
        else
            echo ""
            echo "✗ 手动构建失败！"
        fi
        ;;
        
    *)
        echo "无效选择，退出..."
        exit 1
        ;;
esac

echo ""
echo "========================================"
echo "构建完成"
echo "========================================"
echo ""
echo "部署说明："
echo "1. 将生成的 WAR 文件复制到 Tomcat 的 webapps 目录"
echo "2. 启动 Tomcat 服务器"
echo "3. 访问 http://localhost:8080/xmysfzjjg/"
echo "4. WebService 地址: http://localhost:8080/xmysfzjjg/wservices/"
echo ""
