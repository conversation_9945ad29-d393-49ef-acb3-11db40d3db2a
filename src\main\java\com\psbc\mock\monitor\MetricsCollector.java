package com.psbc.mock.monitor;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 指标收集器
 * 收集和统计各种性能指标和业务指标
 */
@Slf4j
@Component
public class MetricsCollector {

    // 请求计数器
    private final Map<String, AtomicLong> requestCounters = new ConcurrentHashMap<>();
    
    // 成功计数器
    private final Map<String, AtomicLong> successCounters = new ConcurrentHashMap<>();
    
    // 错误计数器
    private final Map<String, AtomicLong> errorCounters = new ConcurrentHashMap<>();
    
    // 响应时间统计
    private final Map<String, ResponseTimeStats> responseTimeStats = new ConcurrentHashMap<>();
    
    // 连接统计
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    
    // 启动时间
    private final LocalDateTime startTime = LocalDateTime.now();

    /**
     * 记录请求
     */
    public void recordRequest(String service, String operation) {
        String key = service + ":" + operation;
        requestCounters.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        log.debug("记录请求: {}", key);
    }

    /**
     * 记录成功响应
     */
    public void recordSuccess(String service, String operation, long responseTime) {
        String key = service + ":" + operation;
        successCounters.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        
        // 记录响应时间
        ResponseTimeStats stats = responseTimeStats.computeIfAbsent(key, k -> new ResponseTimeStats());
        stats.addResponseTime(responseTime);
        
        log.debug("记录成功响应: {}, 响应时间: {}ms", key, responseTime);
    }

    /**
     * 记录错误响应
     */
    public void recordError(String service, String operation, String errorType) {
        String key = service + ":" + operation;
        errorCounters.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        
        // 记录错误类型
        String errorKey = key + ":error:" + errorType;
        errorCounters.computeIfAbsent(errorKey, k -> new AtomicLong(0)).incrementAndGet();
        
        log.debug("记录错误响应: {}, 错误类型: {}", key, errorType);
    }

    /**
     * 记录连接建立
     */
    public void recordConnectionEstablished() {
        activeConnections.incrementAndGet();
        totalConnections.incrementAndGet();
        log.debug("连接建立, 当前活跃连接数: {}", activeConnections.get());
    }

    /**
     * 记录连接关闭
     */
    public void recordConnectionClosed() {
        activeConnections.decrementAndGet();
        log.debug("连接关闭, 当前活跃连接数: {}", activeConnections.get());
    }

    /**
     * 获取服务统计信息
     */
    public ServiceMetrics getServiceMetrics(String service) {
        ServiceMetrics metrics = new ServiceMetrics();
        metrics.setServiceName(service);
        
        long totalRequests = 0;
        long totalSuccess = 0;
        long totalErrors = 0;
        double totalResponseTime = 0;
        int operationCount = 0;
        
        for (Map.Entry<String, AtomicLong> entry : requestCounters.entrySet()) {
            if (entry.getKey().startsWith(service + ":")) {
                String operation = entry.getKey().substring(service.length() + 1);
                long requests = entry.getValue().get();
                long success = successCounters.getOrDefault(entry.getKey(), new AtomicLong(0)).get();
                long errors = errorCounters.getOrDefault(entry.getKey(), new AtomicLong(0)).get();
                
                totalRequests += requests;
                totalSuccess += success;
                totalErrors += errors;
                
                ResponseTimeStats stats = responseTimeStats.get(entry.getKey());
                if (stats != null) {
                    totalResponseTime += stats.getAverageResponseTime();
                    operationCount++;
                }
                
                OperationMetrics opMetrics = new OperationMetrics();
                opMetrics.setOperationName(operation);
                opMetrics.setRequestCount(requests);
                opMetrics.setSuccessCount(success);
                opMetrics.setErrorCount(errors);
                opMetrics.setSuccessRate(requests > 0 ? (double) success / requests * 100 : 0);
                opMetrics.setAverageResponseTime(stats != null ? stats.getAverageResponseTime() : 0);
                opMetrics.setMaxResponseTime(stats != null ? stats.getMaxResponseTime() : 0);
                opMetrics.setMinResponseTime(stats != null ? stats.getMinResponseTime() : 0);
                
                metrics.getOperations().add(opMetrics);
            }
        }
        
        metrics.setTotalRequests(totalRequests);
        metrics.setTotalSuccess(totalSuccess);
        metrics.setTotalErrors(totalErrors);
        metrics.setSuccessRate(totalRequests > 0 ? (double) totalSuccess / totalRequests * 100 : 0);
        metrics.setAverageResponseTime(operationCount > 0 ? totalResponseTime / operationCount : 0);
        
        return metrics;
    }

    /**
     * 获取系统总体指标
     */
    public SystemMetrics getSystemMetrics() {
        SystemMetrics metrics = new SystemMetrics();
        
        long totalRequests = requestCounters.values().stream().mapToLong(AtomicLong::get).sum();
        long totalSuccess = successCounters.values().stream().mapToLong(AtomicLong::get).sum();
        long totalErrors = errorCounters.values().stream().mapToLong(AtomicLong::get).sum();
        
        metrics.setTotalRequests(totalRequests);
        metrics.setTotalSuccess(totalSuccess);
        metrics.setTotalErrors(totalErrors);
        metrics.setSuccessRate(totalRequests > 0 ? (double) totalSuccess / totalRequests * 100 : 0);
        metrics.setActiveConnections(activeConnections.get());
        metrics.setTotalConnections(totalConnections.get());
        metrics.setStartTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        metrics.setUptime(java.time.Duration.between(startTime, LocalDateTime.now()).toSeconds());
        
        // 计算平均响应时间
        double totalResponseTime = responseTimeStats.values().stream()
                .mapToDouble(ResponseTimeStats::getAverageResponseTime)
                .average()
                .orElse(0);
        metrics.setAverageResponseTime(totalResponseTime);
        
        return metrics;
    }

    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        requestCounters.clear();
        successCounters.clear();
        errorCounters.clear();
        responseTimeStats.clear();
        activeConnections.set(0);
        totalConnections.set(0);
        log.info("所有指标已重置");
    }

    /**
     * 响应时间统计
     */
    private static class ResponseTimeStats {
        private final LongAdder totalTime = new LongAdder();
        private final AtomicLong count = new AtomicLong(0);
        private volatile long maxTime = 0;
        private volatile long minTime = Long.MAX_VALUE;

        public void addResponseTime(long responseTime) {
            totalTime.add(responseTime);
            count.incrementAndGet();
            
            // 更新最大值
            long currentMax = maxTime;
            while (responseTime > currentMax && !compareAndSetMax(currentMax, responseTime)) {
                currentMax = maxTime;
            }
            
            // 更新最小值
            long currentMin = minTime;
            while (responseTime < currentMin && !compareAndSetMin(currentMin, responseTime)) {
                currentMin = minTime;
            }
        }

        private boolean compareAndSetMax(long expect, long update) {
            if (maxTime == expect) {
                maxTime = update;
                return true;
            }
            return false;
        }

        private boolean compareAndSetMin(long expect, long update) {
            if (minTime == expect) {
                minTime = update;
                return true;
            }
            return false;
        }

        public double getAverageResponseTime() {
            long c = count.get();
            return c > 0 ? (double) totalTime.sum() / c : 0;
        }

        public long getMaxResponseTime() {
            return maxTime;
        }

        public long getMinResponseTime() {
            return minTime == Long.MAX_VALUE ? 0 : minTime;
        }
    }

    /**
     * 服务指标
     */
    @Data
    public static class ServiceMetrics {
        private String serviceName;
        private long totalRequests;
        private long totalSuccess;
        private long totalErrors;
        private double successRate;
        private double averageResponseTime;
        private List<OperationMetrics> operations = new ArrayList<>();
    }

    /**
     * 操作指标
     */
    @Data
    public static class OperationMetrics {
        private String operationName;
        private long requestCount;
        private long successCount;
        private long errorCount;
        private double successRate;
        private double averageResponseTime;
        private long maxResponseTime;
        private long minResponseTime;
    }

    /**
     * 系统指标
     */
    @Data
    public static class SystemMetrics {
        private long totalRequests;
        private long totalSuccess;
        private long totalErrors;
        private double successRate;
        private double averageResponseTime;
        private long activeConnections;
        private long totalConnections;
        private String startTime;
        private long uptime;
    }
}
