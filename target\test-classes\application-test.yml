server:
  port: 0  # 使用随机端口避免冲突

spring:
  application:
    name: mock-service-test

# 测试环境配置
mock:
  zhbg:
    port: 19091
    enabled: true
  
  webservice:
    port: 18443
    enabled: true
    context-path: /xmysfzjjg/wservices
  
  socket:
    port: 19702
    enabled: false  # 测试时禁用Socket服务

# CXF配置
cxf:
  path: /xmysfzjjg/wservices

# 日志配置
logging:
  level:
    com.psbc.mock: DEBUG
    root: WARN
