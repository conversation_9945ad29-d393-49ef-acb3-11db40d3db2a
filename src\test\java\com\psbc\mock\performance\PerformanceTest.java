package com.psbc.mock.performance;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能测试类
 * 测试挡板服务在高并发和大量请求下的性能表现
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles("test")
public class PerformanceTest {

    private static final String BASE_URL = "http://localhost:9091";
    private static final String SOCKET_HOST = "localhost";
    private static final int SOCKET_PORT = 9702;

    @BeforeEach
    void setUp() throws InterruptedException {
        // 等待服务启动
        Thread.sleep(2000);
    }

    @Test
    void testHttpPerformance() throws Exception {
        log.info("开始HTTP接口性能测试...");
        
        int threadCount = 50;
        int requestsPerThread = 20;
        int totalRequests = threadCount * requestsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(totalRequests);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong errorCount = new AtomicLong(0);
        List<Long> responseTimes = new CopyOnWriteArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    try {
                        long requestStart = System.currentTimeMillis();
                        
                        String requestBody = "{\n" +
                                "  \"serviceno\": \"30001\",\n" +
                                "  \"data\": \"performance test\"\n" +
                                "}";
                        
                        String response = sendPostRequest(BASE_URL + "/api/admin/xzp/queryYxjfLsxdye", 
                                                        requestBody, "application/json");
                        
                        long requestEnd = System.currentTimeMillis();
                        responseTimes.add(requestEnd - requestStart);
                        
                        if (response != null && (response.contains("success") || response.contains("0000"))) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                        
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        log.error("HTTP请求失败", e);
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }
        
        latch.await(60, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        
        executor.shutdown();
        
        // 计算性能指标
        long totalTime = endTime - startTime;
        double tps = (double) totalRequests / (totalTime / 1000.0);
        double avgResponseTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxResponseTime = responseTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long minResponseTime = responseTimes.stream().mapToLong(Long::longValue).min().orElse(0);
        
        log.info("HTTP性能测试结果:");
        log.info("总请求数: {}", totalRequests);
        log.info("成功请求数: {}", successCount.get());
        log.info("失败请求数: {}", errorCount.get());
        log.info("总耗时: {}ms", totalTime);
        log.info("TPS: {:.2f}", tps);
        log.info("平均响应时间: {:.2f}ms", avgResponseTime);
        log.info("最大响应时间: {}ms", maxResponseTime);
        log.info("最小响应时间: {}ms", minResponseTime);
        
        // 断言性能要求
        assertTrue(successCount.get() > totalRequests * 0.95, "成功率应该大于95%");
        assertTrue(avgResponseTime < 1000, "平均响应时间应该小于1秒");
        assertTrue(tps > 10, "TPS应该大于10");
    }

    @Test
    void testSocketPerformance() throws Exception {
        log.info("开始Socket接口性能测试...");
        
        int threadCount = 30;
        int requestsPerThread = 10;
        int totalRequests = threadCount * requestsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(totalRequests);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong errorCount = new AtomicLong(0);
        List<Long> responseTimes = new CopyOnWriteArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    try {
                        long requestStart = System.currentTimeMillis();
                        
                        String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                                "<content>\n" +
                                "  <head>\n" +
                                "    <serviceno>99999</serviceno>\n" +
                                "  </head>\n" +
                                "  <body>\n" +
                                "    <threadId>" + threadId + "</threadId>\n" +
                                "    <requestId>" + j + "</requestId>\n" +
                                "    <data>performance test</data>\n" +
                                "  </body>\n" +
                                "</content>";
                        
                        String response = sendSocketMessage(message);
                        
                        long requestEnd = System.currentTimeMillis();
                        responseTimes.add(requestEnd - requestStart);
                        
                        if (response != null && response.contains("statecode") && response.contains("1")) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                        
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        log.error("Socket请求失败", e);
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }
        
        latch.await(60, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        
        executor.shutdown();
        
        // 计算性能指标
        long totalTime = endTime - startTime;
        double tps = (double) totalRequests / (totalTime / 1000.0);
        double avgResponseTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxResponseTime = responseTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long minResponseTime = responseTimes.stream().mapToLong(Long::longValue).min().orElse(0);
        
        log.info("Socket性能测试结果:");
        log.info("总请求数: {}", totalRequests);
        log.info("成功请求数: {}", successCount.get());
        log.info("失败请求数: {}", errorCount.get());
        log.info("总耗时: {}ms", totalTime);
        log.info("TPS: {:.2f}", tps);
        log.info("平均响应时间: {:.2f}ms", avgResponseTime);
        log.info("最大响应时间: {}ms", maxResponseTime);
        log.info("最小响应时间: {}ms", minResponseTime);
        
        // 断言性能要求
        assertTrue(successCount.get() > totalRequests * 0.95, "成功率应该大于95%");
        assertTrue(avgResponseTime < 500, "平均响应时间应该小于500ms");
        assertTrue(tps > 5, "TPS应该大于5");
    }

    @Test
    void testLongRunningStability() throws Exception {
        log.info("开始长时间运行稳定性测试...");
        
        int duration = 30; // 测试30秒
        int threadCount = 10;
        AtomicLong requestCount = new AtomicLong(0);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong errorCount = new AtomicLong(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        long startTime = System.currentTimeMillis();
        long endTime = startTime + duration * 1000;
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                while (System.currentTimeMillis() < endTime) {
                    try {
                        requestCount.incrementAndGet();
                        
                        if (threadId % 2 == 0) {
                            // HTTP请求
                            String requestBody = "{\n" +
                                    "  \"serviceno\": \"30001\",\n" +
                                    "  \"data\": \"stability test\"\n" +
                                    "}";
                            
                            String response = sendPostRequest(BASE_URL + "/api/admin/xzp/queryYxjfLsxdye", 
                                                            requestBody, "application/json");
                            
                            if (response != null && (response.contains("success") || response.contains("0000"))) {
                                successCount.incrementAndGet();
                            } else {
                                errorCount.incrementAndGet();
                            }
                        } else {
                            // Socket请求
                            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                                    "<content>\n" +
                                    "  <head>\n" +
                                    "    <serviceno>99999</serviceno>\n" +
                                    "  </head>\n" +
                                    "  <body>\n" +
                                    "    <data>stability test</data>\n" +
                                    "  </body>\n" +
                                    "</content>";
                            
                            String response = sendSocketMessage(message);
                            
                            if (response != null && response.contains("statecode") && response.contains("1")) {
                                successCount.incrementAndGet();
                            } else {
                                errorCount.incrementAndGet();
                            }
                        }
                        
                        // 短暂休息
                        Thread.sleep(100);
                        
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        log.error("稳定性测试请求失败", e);
                    }
                }
            });
        }
        
        executor.shutdown();
        executor.awaitTermination(duration + 10, TimeUnit.SECONDS);
        
        long actualDuration = System.currentTimeMillis() - startTime;
        double tps = (double) requestCount.get() / (actualDuration / 1000.0);
        double successRate = (double) successCount.get() / requestCount.get() * 100;
        
        log.info("长时间运行稳定性测试结果:");
        log.info("测试时长: {}ms", actualDuration);
        log.info("总请求数: {}", requestCount.get());
        log.info("成功请求数: {}", successCount.get());
        log.info("失败请求数: {}", errorCount.get());
        log.info("成功率: {:.2f}%", successRate);
        log.info("平均TPS: {:.2f}", tps);
        
        // 断言稳定性要求
        assertTrue(successRate > 95, "长时间运行成功率应该大于95%");
        assertTrue(requestCount.get() > 100, "应该处理足够数量的请求");
    }

    // 辅助方法
    private String sendPostRequest(String urlStr, String requestBody, String contentType) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", contentType);
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);
        conn.connect();
        
        try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8)) {
            out.write(requestBody);
            out.flush();
        }
        
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        
        return sb.toString();
    }

    private String sendSocketMessage(String message) throws Exception {
        try (Socket socket = new Socket(SOCKET_HOST, SOCKET_PORT)) {
            socket.setSoTimeout(10000);
            sendFormattedMessage(socket, message);
            return receiveFormattedMessage(socket);
        }
    }

    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        try (OutputStream out = socket.getOutputStream()) {
            out.write(lengthBytes);
            out.write(messageBytes);
            out.flush();
        }
    }

    private String receiveFormattedMessage(Socket socket) throws IOException {
        try (InputStream in = socket.getInputStream()) {
            byte[] lengthBytes = new byte[6];
            int bytesRead = in.read(lengthBytes);
            if (bytesRead != 6) {
                throw new IOException("无法读取完整的长度字段");
            }
            
            String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
            int messageLength = Integer.parseInt(lengthStr);
            
            byte[] messageBytes = new byte[messageLength];
            int totalBytesRead = 0;
            while (totalBytesRead < messageLength) {
                int currentBytesRead = in.read(messageBytes, totalBytesRead, messageLength - totalBytesRead);
                if (currentBytesRead == -1) {
                    throw new IOException("连接意外关闭");
                }
                totalBytesRead += currentBytesRead;
            }
            
            return new String(messageBytes, StandardCharsets.UTF_8);
        }
    }
}
