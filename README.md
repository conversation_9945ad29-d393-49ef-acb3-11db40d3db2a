# 挡板测试服务

## 项目简介

本项目是一个挡板测试服务，用于模拟下游系统的响应，帮助测试中转服务的连通性。

## 模拟的服务

### 1. 综合办公系统 HTTP 服务 (端口: 9091)

#### 服务号30001接口
- **URL**: `POST http://localhost:9091/api/admin/xzp/queryYxjfLsxdye`
- **Content-Type**: `application/json`
- **功能**: 查询按揭贷款信息
- **请求示例**:
```json
{
  "serviceno": "30001",
  "data": "test request"
}
```
- **响应示例**:
```json
{
  "status": "success",
  "code": "0000",
  "message": "查询成功",
  "data": {
    "loanInfo": {
      "loanId": "LOAN20240701001",
      "amount": "500000.00",
      "status": "APPROVED",
      "approveDate": "2024-07-01"
    }
  },
  "timestamp": "*************"
}
```

#### 服务号20006接口
- **URL**: `POST http://localhost:9091/api/admin/xzp/queryJgzhInfo`
- **Content-Type**: `application/xml`
- **功能**: 查询监管账户变动反馈信息
- **请求示例**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <serviceno>20006</serviceno>
  </head>
  <body>
    <data>test request</data>
  </body>
</content>
```
- **响应示例**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<response>
  <head>
    <statecode>1</statecode>
    <msg>查询成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <accountInfo>
      <accountNo>6217000010001234567</accountNo>
      <balance>1000000.00</balance>
      <status>ACTIVE</status>
      <lastUpdateTime>2024-07-01 14:30:00</lastUpdateTime>
    </accountInfo>
  </body>
</response>
```

### 2. 委托方 WebService 服务 (端口: 9091)

- **WSDL**: `http://localhost:9091/xmysfzjjg/wservices/IWebServiceService?wsdl`
- **方法**: `Execute(String bankId, String inParmeter)`
- **功能**: 模拟委托方系统的WebService调用
- **响应示例**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <result>
      <transactionId>TXN*************</transactionId>
      <status>SUCCESS</status>
      <message>委托方系统处理成功</message>
      <data>
        <bankId>YCYH</bankId>
        <processTime>*************</processTime>
      </data>
    </result>
  </body>
</content>
```

### 3. 外联系统 Socket 服务 (端口: 9702)

- **协议**: 6位长度前缀 + 消息体
- **功能**: 模拟外联系统的Socket通信
- **响应示例**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>
```

## 快速启动

### 方式1: 使用启动脚本 (Windows)
```bash
start.bat
```

### 方式2: 使用Maven命令
```bash
# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run
```

### 方式3: 使用jar包
```bash
# 打包
mvn clean package

# 运行
java -jar target/mock-service-1.0.0.jar
```

## 测试验证

### 1. 测试综合办公系统接口

#### 测试30001接口
```bash
curl -X POST http://localhost:9091/api/admin/xzp/queryYxjfLsxdye \
  -H "Content-Type: application/json" \
  -d '{"serviceno":"30001","data":"test"}'
```

#### 测试20006接口
```bash
curl -X POST http://localhost:9091/api/admin/xzp/queryJgzhInfo \
  -H "Content-Type: application/xml" \
  -d '<?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno></head></content>'
```

### 2. 测试WebService接口

可以使用SoapUI或其他WebService测试工具访问：
`http://localhost:9091/xmysfzjjg/wservices/IWebServiceService?wsdl`

### 3. 测试Socket接口

可以使用telnet或自定义Socket客户端连接：
```bash
telnet localhost 9702
```

## 健康检查

访问健康检查接口确认服务状态：
```bash
curl http://localhost:9091/api/admin/xzp/health
```

## 日志查看

服务启动后，可以在控制台查看详细的请求和响应日志，包括：
- 接收到的请求内容
- 返回的响应内容
- 处理时间统计
- 错误信息

## 配置说明

可以通过修改 `src/main/resources/application.yml` 来调整服务配置：

```yaml
server:
  port: 9091  # HTTP服务端口

mock:
  zhbg:
    port: 9091
    enabled: true  # 是否启用综合办公系统模拟
  
  webservice:
    port: 8443
    enabled: true  # 是否启用WebService模拟
  
  socket:
    port: 9702
    enabled: true  # 是否启用Socket服务模拟
```

## 注意事项

1. 确保端口9091和9702没有被其他程序占用
2. 如需修改端口，请同时更新中转服务的配置文件
3. 服务启动后会自动打印可用的接口信息
4. 所有响应都是模拟数据，仅用于测试连通性

## 故障排除

### 端口占用问题
```bash
# Windows查看端口占用
netstat -ano | findstr :9091
netstat -ano | findstr :9702

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 编译问题
确保已安装：
- JDK 8或更高版本
- Maven 3.6或更高版本

### 连接问题
1. 检查防火墙设置
2. 确认服务已正常启动
3. 查看控制台日志排查错误
