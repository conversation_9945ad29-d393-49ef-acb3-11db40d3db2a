# xmysfzjjg 项目

厦门银行司法执行监管系统 - 传统Java Web项目

## 项目简介

xmysfzjjg是一个基于Spring框架和CXF WebService的传统Java Web应用，主要功能包括：

- **Socket服务**: 处理客户端Socket连接和消息
- **WebService**: 提供SOAP Web服务接口
- **HTTP服务**: 处理HTTP请求和响应
- **综合办公接口**: 与综合办公系统的接口调用
- **委托方系统**: 与委托方系统的数据交互

## 技术栈

- **框架**: Spring 3.0.4
- **WebService**: Apache CXF 2.3.0
- **日志**: Log4j 1.2.8
- **JSON处理**: FastJSON 1.2.76
- **XML处理**: DOM4J 1.6.1
- **HTTP客户端**: Apache HttpClient 4.5.2
- **工具类**: Hutool 5.0.7

## 快速开始

### 1. 构建WAR包

#### 使用构建脚本（推荐）
```bash
# Windows
build-war.bat

# Linux/Mac
chmod +x build-war.sh
./build-war.sh
```

#### 使用Ant
```bash
ant clean war
```

#### 使用Maven
```bash
mvn clean package
```

### 2. 验证WAR包
```bash
# Windows
verify-war.bat

# Linux/Mac
chmod +x verify-war.sh
./verify-war.sh
```

### 3. 部署到Tomcat
1. 将生成的WAR文件复制到Tomcat的webapps目录
2. 启动Tomcat服务器
3. 访问应用

## 构建选项

### 方式一：交互式构建脚本
- **Windows**: `build-war.bat`
- **Linux/Mac**: `build-war.sh`

提供三种构建方式选择：
1. Ant构建
2. Maven构建  
3. 手动构建

### 方式二：Ant构建
```bash
# 查看帮助
ant help

# 清理并构建
ant clean war

# 查看项目信息
ant info

# 部署到Tomcat
ant deploy
```

**输出**: `dist/xmysfzjjg.war`

### 方式三：Maven构建
```bash
# 构建WAR包
mvn clean package

# 编译源代码
mvn compile

# 部署到Tomcat
mvn tomcat7:deploy
```

**输出**: `target/xmysfzjjg.war`

### 方式四：手动构建
使用jar命令手动打包，适用于没有构建工具的环境。

## 项目结构

```
xmysfzjjg/
├── src/                          # Java源代码
│   ├── com/psbc/client/          # 客户端代码
│   ├── com/psbc/httpApplication/ # HTTP应用
│   ├── com/psbc/socket/          # Socket服务
│   ├── com/psbc/util/            # 工具类
│   ├── com/psbc/webservice/      # WebService
│   ├── client-beans.xml          # 客户端Bean配置
│   ├── spring.xml                # Spring主配置
│   ├── xmysfzjjg.properties      # 应用配置
│   ├── zhbgConf.properties       # 综合办公配置
│   └── log4j.properties          # 日志配置
├── WebContent/                   # Web应用内容
│   ├── WEB-INF/
│   │   ├── web.xml               # Web应用配置
│   │   └── lib/                  # 依赖库(70+个JAR文件)
│   └── META-INF/
├── build/classes/                # 编译后的类文件
├── build.xml                     # Ant构建脚本
├── pom.xml                       # Maven构建配置
├── build-war.bat                 # Windows构建脚本
├── build-war.sh                  # Linux/Mac构建脚本
├── verify-war.bat                # WAR包验证脚本
├── DEPLOYMENT_GUIDE.md           # 详细部署指南
└── README.md                     # 本文件
```

## 配置说明

### 应用配置 (xmysfzjjg.properties)
```properties
IP=127.0.0.1          # Socket服务IP
PORT=9702             # Socket服务端口
WG_PORT=8888          # 外联服务端口
HTTP_PORT=9999        # HTTP服务端口
MESSAGE=...           # 默认响应消息
```

### 综合办公配置 (zhbgConf.properties)
```properties
zhbgPostUrl=http://**************:9090/    # 综合办公基础URL
url30001=api/admin/xzp/queryYxjfLsxdye     # 30001接口路径
url20006=api/admin/xzp/queryJgzhInfo       # 20006接口路径
reqSysNo=350200001                         # 请求系统号
zhbgPublicKey=...                          # 综合办公公钥
```

## 部署说明

### 环境要求
- **Java**: JDK 1.8+
- **Tomcat**: 8.5+ 或 9.0+
- **内存**: 建议2GB+

### 部署步骤
1. 构建WAR包
2. 复制到Tomcat webapps目录
3. 启动Tomcat
4. 验证部署

### 访问地址
- **应用首页**: http://localhost:8080/xmysfzjjg/
- **WebService WSDL**: http://localhost:8080/xmysfzjjg/wservices/IWebServiceService?wsdl

## 服务功能

### Socket服务
- 监听指定端口接收客户端连接
- 处理JSON和XML格式消息
- 根据服务号路由到不同的处理逻辑

### WebService
- 提供SOAP Web服务接口
- 支持Execute方法处理业务请求
- 与委托方系统进行数据交互

### HTTP服务
- 处理HTTP请求
- 支持综合办公系统接口调用
- 提供RESTful风格的API

## 故障排查

### 常见问题
1. **编译错误**: 检查依赖库和Java版本
2. **部署失败**: 查看Tomcat日志
3. **服务不可用**: 检查端口和网络配置
4. **配置错误**: 验证配置文件格式

### 日志位置
- **Tomcat日志**: `$CATALINA_HOME/logs/`
- **应用日志**: 根据log4j配置

## 开发说明

### 主要类说明
- **PayServerSocket**: Socket服务器主类
- **ServerThread**: Socket连接处理线程
- **WebServiceImpl**: WebService实现类
- **ConfigUtil**: 配置工具类
- **ZhbgConfigUtil**: 综合办公配置工具类

### 扩展开发
1. 添加新的业务逻辑到相应的包中
2. 更新Spring配置文件
3. 重新构建和部署

## 版本历史

- **v1.0.0**: 初始版本，包含基础功能

## 支持

如有问题，请查看：
1. `DEPLOYMENT_GUIDE.md` - 详细部署指南
2. Tomcat日志文件
3. 应用配置文件

## 许可证

内部项目，仅供邮储银行厦门分行使用。
