# xmysfzjjg WAR包构建和部署指南

## 项目概述

xmysfzjjg是一个传统的Java Web项目，使用Spring框架和CXF WebService。本指南将帮助您将项目打包成WAR文件并部署到Tomcat服务器。

## 项目结构

```
xmysfzjjg/
├── src/                    # Java源代码
│   ├── com/psbc/client/    # 客户端代码
│   ├── com/psbc/httpApplication/ # HTTP应用
│   ├── com/psbc/socket/    # Socket服务
│   ├── com/psbc/util/      # 工具类
│   ├── com/psbc/webservice/ # WebService
│   ├── *.xml               # Spring配置文件
│   └── *.properties        # 配置文件
├── WebContent/             # Web应用内容
│   ├── WEB-INF/
│   │   ├── web.xml         # Web应用配置
│   │   └── lib/            # 依赖库
│   └── META-INF/
├── build/classes/          # 编译后的类文件
├── build.xml               # Ant构建脚本
├── pom.xml                 # Maven构建配置
├── build-war.bat           # Windows构建脚本
└── build-war.sh            # Linux/Mac构建脚本
```

## 构建方式

### 方式一：使用构建脚本（推荐）

#### Windows
```bash
build-war.bat
```

#### Linux/Mac
```bash
chmod +x build-war.sh
./build-war.sh
```

脚本会提供三种构建选项：
1. Ant构建
2. Maven构建
3. 手动构建

### 方式二：使用Ant构建

#### 前提条件
- 安装Apache Ant
- 确保Java环境可用

#### 构建命令
```bash
# 查看可用目标
ant help

# 清理并构建WAR包
ant clean war

# 显示项目信息
ant info

# 部署到Tomcat（需要设置CATALINA_HOME环境变量）
ant deploy

# 显示手动部署说明
ant deploy-manual
```

#### 输出文件
- WAR文件：`dist/xmysfzjjg.war`

### 方式三：使用Maven构建

#### 前提条件
- 安装Apache Maven
- 确保Java环境可用

#### 构建命令
```bash
# 清理并构建WAR包
mvn clean package

# 编译源代码
mvn compile

# 运行测试（如果有）
mvn test

# 部署到Tomcat（需要配置Tomcat Manager）
mvn tomcat7:deploy
```

#### 输出文件
- WAR文件：`target/xmysfzjjg.war`

### 方式四：手动构建

#### 前提条件
- Java环境可用（包含jar命令）
- 项目已编译（存在build/classes目录）

#### 构建步骤
```bash
# 1. 创建临时目录
mkdir temp_war

# 2. 复制WebContent内容
cp -r WebContent/* temp_war/

# 3. 复制编译后的类文件
cp -r build/classes/* temp_war/WEB-INF/classes/

# 4. 创建WAR文件
cd temp_war
jar -cvf ../xmysfzjjg.war *
cd ..

# 5. 清理临时文件
rm -rf temp_war
```

## 部署到Tomcat

### 1. 准备Tomcat环境

#### 下载和安装Tomcat
- 下载Tomcat 8.5或9.0版本
- 解压到合适的目录
- 设置CATALINA_HOME环境变量

#### 配置Tomcat用户（可选）
编辑`$CATALINA_HOME/conf/tomcat-users.xml`：
```xml
<tomcat-users>
    <role rolename="manager-gui"/>
    <role rolename="manager-script"/>
    <user username="admin" password="admin" roles="manager-gui,manager-script"/>
</tomcat-users>
```

### 2. 部署WAR文件

#### 方法一：自动部署
```bash
# 复制WAR文件到webapps目录
cp xmysfzjjg.war $CATALINA_HOME/webapps/

# 启动Tomcat
$CATALINA_HOME/bin/startup.sh    # Linux/Mac
$CATALINA_HOME/bin/startup.bat   # Windows
```

#### 方法二：使用Tomcat Manager
1. 访问 http://localhost:8080/manager/html
2. 登录（使用配置的用户名密码）
3. 在"WAR file to deploy"部分选择WAR文件
4. 点击"Deploy"

#### 方法三：手动部署
1. 停止Tomcat
2. 删除webapps目录下的旧应用
3. 复制新的WAR文件到webapps目录
4. 启动Tomcat

### 3. 验证部署

#### 检查应用状态
- 访问：http://localhost:8080/xmysfzjjg/
- WebService WSDL：http://localhost:8080/xmysfzjjg/wservices/IWebServiceService?wsdl

#### 检查日志
```bash
# 查看Tomcat日志
tail -f $CATALINA_HOME/logs/catalina.out

# 查看应用日志
tail -f $CATALINA_HOME/logs/localhost.*.log
```

## 配置说明

### 应用配置文件

#### xmysfzjjg.properties
```properties
IP=127.0.0.1
PORT=9702
WG_PORT=8888
HTTP_PORT=9999
MESSAGE=<?xml version="1.0" encoding="utf-8"?>...
```

#### zhbgConf.properties
```properties
zhbgPostUrl=http://**************:9090/
url30001=api/admin/xzp/queryYxjfLsxdye
url20006=api/admin/xzp/queryJgzhInfo
reqSysNo=350200001
zhbgPublicKey=0438b7e7107b34d58ace4edea7a3526ba43b10cb430b1e29fe652c8acae44534f967837760983a41ae3d96635623abd70187023a5f21a67c7af6966e06e728c7f3
```

#### log4j.properties
```properties
log4j.rootLogger=INFO, stdout, file
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%p] %c: %m%n
```

### 环境特定配置

根据部署环境修改配置文件：

#### 开发环境
- 使用本地IP和端口
- 启用详细日志

#### 测试环境
- 使用测试服务器地址
- 适中的日志级别

#### 生产环境
- 使用生产服务器地址
- 最小化日志输出
- 启用安全配置

## 故障排查

### 常见问题

#### 1. 编译错误
```
错误：找不到符号
```
**解决方案**：
- 检查依赖库是否完整
- 确保Java版本兼容（推荐JDK 1.8）

#### 2. 部署失败
```
应用启动失败
```
**解决方案**：
- 检查Tomcat日志
- 验证web.xml配置
- 确保端口未被占用

#### 3. WebService不可访问
```
404 Not Found
```
**解决方案**：
- 检查CXF配置
- 验证Spring配置文件
- 确保WebService类正确注册

#### 4. 数据库连接失败
```
Connection refused
```
**解决方案**：
- 检查数据库服务状态
- 验证连接配置
- 确保网络连通性

### 日志分析

#### 启动日志
```
INFO: Deploying web application archive [xmysfzjjg.war]
INFO: Deployment of web application archive [xmysfzjjg.war] has finished
```

#### 错误日志
```
SEVERE: Exception starting filter [CXFServlet]
ERROR: Failed to initialize WebService
```

## 性能优化

### JVM参数
```bash
export CATALINA_OPTS="-Xms512m -Xmx2048m -XX:PermSize=256m -XX:MaxPermSize=512m"
```

### Tomcat配置
编辑`$CATALINA_HOME/conf/server.xml`：
```xml
<Connector port="8080" protocol="HTTP/1.1"
           connectionTimeout="20000"
           maxThreads="200"
           minSpareThreads="10"
           maxSpareThreads="50"/>
```

## 安全配置

### 1. 移除默认应用
删除webapps目录下的：
- ROOT
- examples
- docs
- manager（如不需要）

### 2. 配置SSL（可选）
```xml
<Connector port="8443" protocol="org.apache.coyote.http11.Http11NioProtocol"
           maxThreads="150" SSLEnabled="true">
    <SSLHostConfig>
        <Certificate certificateKeystoreFile="conf/localhost-rsa.jks"
                     type="RSA" />
    </SSLHostConfig>
</Connector>
```

### 3. 访问控制
配置防火墙规则，限制访问端口。

## 监控和维护

### 1. 应用监控
- 使用JConsole或VisualVM监控JVM
- 配置应用性能监控

### 2. 日志管理
- 定期清理日志文件
- 配置日志轮转

### 3. 备份策略
- 定期备份WAR文件
- 备份配置文件
- 备份数据库

## 总结

本指南提供了多种构建和部署xmysfzjjg项目的方法。推荐使用提供的构建脚本进行快速构建，使用Tomcat进行部署。在生产环境中，请注意安全配置和性能优化。
