package com.psbc.webservice;

import com.psbc.util.ConfigUtil;
import com.psbc.util.Wlhost;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

@WebService(serviceName = "IWebServiceService")
public class WebServiceImpl implements IWebService {
  private static final Logger logger = LogManager.getLogger(WebServiceImpl.class);
  
  @WebMethod
  @WebResult(targetNamespace = "http://webservice.psbc.com/")
  public String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/") String s) {
    return "********";
  }
  
  
  @WebMethod
  @WebResult(targetNamespace = "http://webservice.psbc.com/")
  public String Execute(@WebParam(targetNamespace = "http://webservice.psbc.com/") String BankID, String inParmeter) {
    String rspmsg = null;
    logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>");
    logger.info("BankID:" + BankID + " 报文inParmeter:"+ inParmeter.trim());
    Wlhost wl = new Wlhost();
    String ip = ConfigUtil.getvalue("IP");
    String port = ConfigUtil.getvalue("PORT");
    long start = System.nanoTime();
    logger.info("报文发往外联系统开始>>>");
    rspmsg = wl.sendToWl(ip, Integer.parseInt(port), inParmeter.trim());
    long end = System.nanoTime();
    logger.info("报文发往外联系统结束>>>");
    logger.info("外联系统返回报文耗时:"+ ((end - start) / 1000L / 1000L) + "毫秒");
    logger.info("外联系统返回报文长度"+ rspmsg.length());
    logger.info("外联系统返回报文:["+ rspmsg + "]");
    return rspmsg;
  }
}
