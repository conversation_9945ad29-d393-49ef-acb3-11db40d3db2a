<?xml version="1.0" encoding="UTF-8"?>
<project name="xmysfzjjg" default="war" basedir=".">
    
    <!-- 项目属性定义 -->
    <property name="project.name" value="xmysfzjjg"/>
    <property name="project.version" value="1.0.0"/>
    
    <!-- 目录定义 -->
    <property name="src.dir" value="src"/>
    <property name="build.dir" value="build"/>
    <property name="classes.dir" value="${build.dir}/classes"/>
    <property name="webcontent.dir" value="WebContent"/>
    <property name="lib.dir" value="${webcontent.dir}/WEB-INF/lib"/>
    <property name="dist.dir" value="dist"/>
    <property name="war.file" value="${dist.dir}/${project.name}.war"/>
    
    <!-- 类路径定义 -->
    <path id="classpath">
        <fileset dir="${lib.dir}">
            <include name="*.jar"/>
        </fileset>
    </path>
    
    <!-- 清理目标 -->
    <target name="clean" description="清理构建目录">
        <delete dir="${build.dir}"/>
        <delete dir="${dist.dir}"/>
        <echo message="清理完成"/>
    </target>
    
    <!-- 初始化目标 -->
    <target name="init" depends="clean" description="初始化构建环境">
        <mkdir dir="${build.dir}"/>
        <mkdir dir="${classes.dir}"/>
        <mkdir dir="${dist.dir}"/>
        <echo message="构建环境初始化完成"/>
    </target>
    
    <!-- 编译目标 -->
    <target name="compile" depends="init" description="编译Java源代码">
        <echo message="开始编译Java源代码..."/>
        
        <!-- 编译Java文件 -->
        <javac srcdir="${src.dir}" 
               destdir="${classes.dir}" 
               classpathref="classpath"
               encoding="UTF-8"
               debug="true"
               includeantruntime="false">
            <include name="**/*.java"/>
        </javac>
        
        <!-- 复制配置文件 -->
        <copy todir="${classes.dir}">
            <fileset dir="${src.dir}">
                <include name="**/*.xml"/>
                <include name="**/*.properties"/>
            </fileset>
        </copy>
        
        <echo message="编译完成"/>
    </target>
    
    <!-- 打包WAR目标 -->
    <target name="war" depends="compile" description="打包WAR文件">
        <echo message="开始打包WAR文件..."/>
        
        <!-- 创建WAR文件 -->
        <war destfile="${war.file}" webxml="${webcontent.dir}/WEB-INF/web.xml">
            
            <!-- 添加WebContent目录内容（除了WEB-INF） -->
            <fileset dir="${webcontent.dir}">
                <exclude name="WEB-INF/**"/>
            </fileset>
            
            <!-- 添加编译后的类文件 -->
            <classes dir="${classes.dir}"/>
            
            <!-- 添加依赖库 -->
            <lib dir="${lib.dir}"/>
            
            <!-- 添加WEB-INF目录（除了lib和classes） -->
            <webinf dir="${webcontent.dir}/WEB-INF">
                <exclude name="lib/**"/>
                <exclude name="classes/**"/>
            </webinf>
            
            <!-- 添加META-INF -->
            <metainf dir="${webcontent.dir}/META-INF"/>
            
        </war>
        
        <echo message="WAR文件创建完成: ${war.file}"/>
        <echo message="文件大小: "/>
        <length file="${war.file}" property="war.size"/>
        <echo message="${war.size} bytes"/>
    </target>
    
    <!-- 部署目标（可选） -->
    <target name="deploy" depends="war" description="部署到Tomcat">
        <property name="tomcat.webapps" value="${env.CATALINA_HOME}/webapps"/>
        
        <!-- 检查Tomcat环境变量 -->
        <available file="${tomcat.webapps}" type="dir" property="tomcat.available"/>
        
        <antcall target="deploy-to-tomcat"/>
    </target>
    
    <target name="deploy-to-tomcat" if="tomcat.available">
        <echo message="部署到Tomcat: ${tomcat.webapps}"/>
        
        <!-- 停止可能存在的应用 -->
        <delete dir="${tomcat.webapps}/${project.name}"/>
        <delete file="${tomcat.webapps}/${project.name}.war"/>
        
        <!-- 复制WAR文件 -->
        <copy file="${war.file}" todir="${tomcat.webapps}"/>
        
        <echo message="部署完成！"/>
        <echo message="访问地址: http://localhost:8080/${project.name}/"/>
    </target>
    
    <target name="deploy-manual" depends="war" description="手动部署说明">
        <echo message=""/>
        <echo message="========================================"/>
        <echo message="手动部署说明"/>
        <echo message="========================================"/>
        <echo message="1. 将 ${war.file} 复制到 Tomcat 的 webapps 目录"/>
        <echo message="2. 启动 Tomcat 服务器"/>
        <echo message="3. 访问 http://localhost:8080/${project.name}/"/>
        <echo message="4. WebService地址: http://localhost:8080/${project.name}/wservices/"/>
        <echo message="========================================"/>
    </target>
    
    <!-- 信息目标 -->
    <target name="info" description="显示项目信息">
        <echo message=""/>
        <echo message="========================================"/>
        <echo message="项目信息"/>
        <echo message="========================================"/>
        <echo message="项目名称: ${project.name}"/>
        <echo message="项目版本: ${project.version}"/>
        <echo message="源码目录: ${src.dir}"/>
        <echo message="构建目录: ${build.dir}"/>
        <echo message="Web目录: ${webcontent.dir}"/>
        <echo message="WAR文件: ${war.file}"/>
        <echo message="========================================"/>
    </target>
    
    <!-- 帮助目标 -->
    <target name="help" description="显示帮助信息">
        <echo message=""/>
        <echo message="========================================"/>
        <echo message="可用的构建目标"/>
        <echo message="========================================"/>
        <echo message="clean        - 清理构建目录"/>
        <echo message="compile      - 编译Java源代码"/>
        <echo message="war          - 打包WAR文件"/>
        <echo message="deploy       - 自动部署到Tomcat"/>
        <echo message="deploy-manual- 显示手动部署说明"/>
        <echo message="info         - 显示项目信息"/>
        <echo message="help         - 显示此帮助信息"/>
        <echo message="========================================"/>
        <echo message="使用方法: ant [目标名称]"/>
        <echo message="例如: ant war"/>
        <echo message="========================================"/>
    </target>
    
</project>
