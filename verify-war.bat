@echo off
echo ========================================
echo xmysfzjjg WAR包验证脚本
echo ========================================

cd /d "%~dp0"

echo.
echo 检查WAR文件...

REM 检查各种可能的WAR文件位置
set WAR_FILE=
if exist "dist\xmysfzjjg.war" (
    set WAR_FILE=dist\xmysfzjjg.war
    echo ✓ 找到Ant构建的WAR文件: %WAR_FILE%
) else if exist "target\xmysfzjjg.war" (
    set WAR_FILE=target\xmysfzjjg.war
    echo ✓ 找到Maven构建的WAR文件: %WAR_FILE%
) else if exist "xmysfzjjg.war" (
    set WAR_FILE=xmysfzjjg.war
    echo ✓ 找到手动构建的WAR文件: %WAR_FILE%
) else (
    echo ✗ 未找到WAR文件！
    echo.
    echo 请先构建WAR文件：
    echo - 运行 build-war.bat
    echo - 或使用 ant war
    echo - 或使用 mvn package
    goto end
)

echo.
echo ========================================
echo WAR文件信息
echo ========================================

REM 显示文件大小
for %%A in ("%WAR_FILE%") do (
    echo 文件名: %%~nxA
    echo 文件大小: %%~zA 字节
    echo 修改时间: %%~tA
)

echo.
echo ========================================
echo WAR文件内容验证
echo ========================================

REM 创建临时目录
if exist temp_verify rmdir /s /q temp_verify
mkdir temp_verify

echo.
echo 解压WAR文件进行验证...
cd temp_verify
jar -xf "..\%WAR_FILE%"

echo.
echo 检查关键文件和目录...

REM 检查web.xml
if exist "WEB-INF\web.xml" (
    echo ✓ web.xml 存在
) else (
    echo ✗ web.xml 缺失
)

REM 检查类文件
if exist "WEB-INF\classes" (
    echo ✓ classes 目录存在
    dir /s /b WEB-INF\classes\*.class > nul 2>&1
    if %errorlevel% == 0 (
        echo ✓ 包含编译后的类文件
    ) else (
        echo ✗ 未找到编译后的类文件
    )
) else (
    echo ✗ classes 目录缺失
)

REM 检查依赖库
if exist "WEB-INF\lib" (
    echo ✓ lib 目录存在
    for /f %%i in ('dir /b WEB-INF\lib\*.jar 2^>nul ^| find /c /v ""') do (
        echo ✓ 包含 %%i 个JAR依赖库
    )
) else (
    echo ✗ lib 目录缺失
)

REM 检查配置文件
if exist "WEB-INF\classes\*.properties" (
    echo ✓ 包含配置文件
) else (
    echo ✗ 未找到配置文件
)

if exist "WEB-INF\classes\*.xml" (
    echo ✓ 包含Spring配置文件
) else (
    echo ✗ 未找到Spring配置文件
)

echo.
echo ========================================
echo 详细内容列表
echo ========================================

echo.
echo 主要Java类:
if exist "WEB-INF\classes\com\psbc" (
    dir /s /b WEB-INF\classes\com\psbc\*.class
) else (
    echo 未找到Java类文件
)

echo.
echo 配置文件:
if exist "WEB-INF\classes\*.properties" (
    dir /b WEB-INF\classes\*.properties
)
if exist "WEB-INF\classes\*.xml" (
    dir /b WEB-INF\classes\*.xml
)

echo.
echo 依赖库 (前10个):
if exist "WEB-INF\lib" (
    dir /b WEB-INF\lib\*.jar | head -10
)

cd ..
rmdir /s /q temp_verify

echo.
echo ========================================
echo 部署建议
echo ========================================

echo.
echo WAR文件验证完成！
echo.
echo 部署步骤：
echo 1. 将 %WAR_FILE% 复制到 Tomcat 的 webapps 目录
echo 2. 启动 Tomcat 服务器
echo 3. 访问 http://localhost:8080/xmysfzjjg/
echo 4. WebService WSDL: http://localhost:8080/xmysfzjjg/wservices/IWebServiceService?wsdl
echo.
echo 注意事项：
echo - 确保 Tomcat 版本兼容 (推荐 8.5 或 9.0)
echo - 检查端口 8080 是否可用
echo - 确保 Java 版本兼容 (推荐 JDK 1.8)
echo - 根据需要修改配置文件中的IP和端口设置

:end
echo.
pause
