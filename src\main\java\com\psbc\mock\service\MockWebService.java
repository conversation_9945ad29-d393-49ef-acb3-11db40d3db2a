package com.psbc.mock.service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * 委托方WebService接口定义
 * 模拟ClientIWebService接口
 */
@WebService(
    targetNamespace = "http://tempuri.org/"
)
public interface MockWebService {
    
    @WebMethod(
        operationName = "Execute",
        action = "http://tempuri.org/Execute"
    )
    @WebResult(
        name = "ExecuteResult",
        targetNamespace = "http://tempuri.org/"
    )
    String Execute(
        @WebParam(targetNamespace = "http://tempuri.org/", name = "BankID") String bankId, 
        @WebParam(targetNamespace = "http://tempuri.org/", name = "inParmeter") String inParmeter
    );
}
