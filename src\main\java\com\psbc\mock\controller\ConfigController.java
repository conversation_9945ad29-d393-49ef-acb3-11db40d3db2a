package com.psbc.mock.controller;

import com.psbc.mock.config.MockConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 配置管理控制器
 * 提供动态配置管理的REST接口
 */
@Slf4j
@RestController
@RequestMapping("/api/config")
public class ConfigController {

    @Autowired
    private MockConfigManager configManager;

    /**
     * 获取所有响应模板
     */
    @GetMapping("/templates")
    public ResponseEntity<Map<String, MockConfigManager.ResponseTemplate>> getAllTemplates() {
        return ResponseEntity.ok(configManager.getAllResponseTemplates());
    }

    /**
     * 获取指定响应模板
     */
    @GetMapping("/templates/{serviceNo}/{type}")
    public ResponseEntity<MockConfigManager.ResponseTemplate> getTemplate(
            @PathVariable String serviceNo, 
            @PathVariable String type) {
        MockConfigManager.ResponseTemplate template = configManager.getResponseTemplate(serviceNo, type);
        if (template != null) {
            return ResponseEntity.ok(template);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 设置响应模板
     */
    @PostMapping("/templates/{serviceNo}/{type}")
    public ResponseEntity<String> setTemplate(
            @PathVariable String serviceNo,
            @PathVariable String type,
            @RequestBody String template) {
        try {
            configManager.setResponseTemplate(serviceNo, type, template);
            return ResponseEntity.ok("{\"message\":\"响应模板设置成功\"}");
        } catch (Exception e) {
            log.error("设置响应模板失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"设置响应模板失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取所有服务配置
     */
    @GetMapping("/services")
    public ResponseEntity<Map<String, MockConfigManager.ServiceConfig>> getAllServiceConfigs() {
        return ResponseEntity.ok(configManager.getAllServiceConfigs());
    }

    /**
     * 获取指定服务配置
     */
    @GetMapping("/services/{serviceName}")
    public ResponseEntity<MockConfigManager.ServiceConfig> getServiceConfig(@PathVariable String serviceName) {
        MockConfigManager.ServiceConfig config = configManager.getServiceConfig(serviceName);
        if (config != null) {
            return ResponseEntity.ok(config);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 设置服务配置
     */
    @PostMapping("/services/{serviceName}")
    public ResponseEntity<String> setServiceConfig(
            @PathVariable String serviceName,
            @RequestBody MockConfigManager.ServiceConfig config) {
        try {
            config.setServiceName(serviceName);
            configManager.setServiceConfig(serviceName, config);
            return ResponseEntity.ok("{\"message\":\"服务配置设置成功\"}");
        } catch (Exception e) {
            log.error("设置服务配置失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"设置服务配置失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 批量设置服务延迟
     */
    @PostMapping("/delay/{delay}")
    public ResponseEntity<String> setGlobalDelay(@PathVariable int delay) {
        try {
            configManager.getAllServiceConfigs().values().forEach(config -> {
                config.setResponseDelay(delay);
            });
            log.info("全局响应延迟已设置为: {}ms", delay);
            return ResponseEntity.ok("{\"message\":\"全局响应延迟已设置为" + delay + "ms\"}");
        } catch (Exception e) {
            log.error("设置全局延迟失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"设置全局延迟失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 批量设置服务错误率
     */
    @PostMapping("/error-rate/{rate}")
    public ResponseEntity<String> setGlobalErrorRate(@PathVariable int rate) {
        try {
            if (rate < 0 || rate > 100) {
                return ResponseEntity.badRequest().body("{\"error\":\"错误率必须在0-100之间\"}");
            }
            
            configManager.getAllServiceConfigs().values().forEach(config -> {
                config.setErrorRate(rate);
            });
            log.info("全局错误率已设置为: {}%", rate);
            return ResponseEntity.ok("{\"message\":\"全局错误率已设置为" + rate + "%\"}");
        } catch (Exception e) {
            log.error("设置全局错误率失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"设置全局错误率失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 启用/禁用服务
     */
    @PostMapping("/services/{serviceName}/enabled/{enabled}")
    public ResponseEntity<String> setServiceEnabled(
            @PathVariable String serviceName,
            @PathVariable boolean enabled) {
        try {
            MockConfigManager.ServiceConfig config = configManager.getServiceConfig(serviceName);
            if (config == null) {
                return ResponseEntity.notFound().build();
            }
            
            config.setEnabled(enabled);
            configManager.setServiceConfig(serviceName, config);
            
            String status = enabled ? "启用" : "禁用";
            log.info("服务 {} 已{}", serviceName, status);
            return ResponseEntity.ok("{\"message\":\"服务 " + serviceName + " 已" + status + "\"}");
        } catch (Exception e) {
            log.error("设置服务状态失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"设置服务状态失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 重置所有配置为默认值
     */
    @PostMapping("/reset")
    public ResponseEntity<String> resetConfigs() {
        try {
            // 重置服务配置
            configManager.getAllServiceConfigs().values().forEach(config -> {
                config.setEnabled(true);
                config.setResponseDelay(100);
                config.setErrorRate(0);
            });
            
            log.info("所有配置已重置为默认值");
            return ResponseEntity.ok("{\"message\":\"所有配置已重置为默认值\"}");
        } catch (Exception e) {
            log.error("重置配置失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"重置配置失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取配置管理帮助信息
     */
    @GetMapping("/help")
    public ResponseEntity<String> getHelp() {
        String help = "{\n" +
                "  \"title\": \"挡板服务配置管理API\",\n" +
                "  \"endpoints\": {\n" +
                "    \"GET /api/config/templates\": \"获取所有响应模板\",\n" +
                "    \"GET /api/config/templates/{serviceNo}/{type}\": \"获取指定响应模板\",\n" +
                "    \"POST /api/config/templates/{serviceNo}/{type}\": \"设置响应模板\",\n" +
                "    \"GET /api/config/services\": \"获取所有服务配置\",\n" +
                "    \"GET /api/config/services/{serviceName}\": \"获取指定服务配置\",\n" +
                "    \"POST /api/config/services/{serviceName}\": \"设置服务配置\",\n" +
                "    \"POST /api/config/delay/{delay}\": \"设置全局响应延迟(ms)\",\n" +
                "    \"POST /api/config/error-rate/{rate}\": \"设置全局错误率(0-100)\",\n" +
                "    \"POST /api/config/services/{serviceName}/enabled/{enabled}\": \"启用/禁用服务\",\n" +
                "    \"POST /api/config/reset\": \"重置所有配置为默认值\",\n" +
                "    \"GET /api/config/help\": \"获取帮助信息\"\n" +
                "  },\n" +
                "  \"serviceNames\": [\"ZHBG\", \"WEBSERVICE\", \"SOCKET\"],\n" +
                "  \"serviceNumbers\": [\"30001\", \"20006\", \"99999\"],\n" +
                "  \"responseTypes\": [\"SUCCESS\", \"ERROR\"]\n" +
                "}";
        return ResponseEntity.ok(help);
    }
}
