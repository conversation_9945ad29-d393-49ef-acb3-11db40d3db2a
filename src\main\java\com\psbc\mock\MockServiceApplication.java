package com.psbc.mock;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import lombok.extern.slf4j.Slf4j;

/**
 * 挡板测试服务主启动类
 * 模拟下游系统响应，用于测试中转服务的连通性
 */
@Slf4j
@SpringBootApplication
public class MockServiceApplication {

    public static void main(String[] args) {
        log.info("启动挡板测试服务...");
        SpringApplication.run(MockServiceApplication.class, args);
        log.info("挡板测试服务启动完成");
        
        // 打印服务信息
        log.info("=== 挡板测试服务信息 ===");
        log.info("综合办公系统HTTP服务: http://localhost:9091");
        log.info("  - 30001接口: POST http://localhost:9091/api/admin/xzp/queryYxjfLsxdye");
        log.info("  - 20006接口: POST http://localhost:9091/api/admin/xzp/queryJgzhInfo");
        log.info("委托方WebService服务: https://localhost:8088/xmysfzjjg/wservices/IWebServiceService?wsdl");
        log.info("外联系统Socket服务: localhost:9702");
        log.info("========================");
    }
}
