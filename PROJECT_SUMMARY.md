# 挡板测试服务项目总结

## 项目概述

本项目是一个完整的挡板测试服务，用于模拟中转服务的下游系统响应，确保中转服务的WebService和Socket服务请求可达性测试。

## 项目结构

```
mock-work/
├── pom.xml                                    # Maven项目配置
├── start.bat                                  # Windows启动脚本
├── README.md                                  # 项目使用说明
├── PROJECT_SUMMARY.md                         # 项目总结文档
└── src/
    ├── main/
    │   ├── java/com/psbc/mock/
    │   │   ├── MockServiceApplication.java    # Spring Boot主启动类
    │   │   ├── config/
    │   │   │   └── WebServiceConfig.java      # WebService配置类
    │   │   ├── controller/
    │   │   │   └── ZhbgController.java        # 综合办公系统HTTP接口
    │   │   ├── service/
    │   │   │   ├── MockWebService.java        # WebService接口定义
    │   │   │   └── impl/
    │   │   │       └── MockWebServiceImpl.java # WebService实现类
    │   │   └── socket/
    │   │       ├── MockSocketServer.java      # Socket服务器
    │   │       ├── MockSocketDecoder.java     # Socket消息解码器
    │   │       ├── MockSocketEncoder.java     # Socket消息编码器
    │   │       └── MockSocketHandler.java     # Socket消息处理器
    │   └── resources/
    │       └── application.yml                # 应用配置文件
    └── test/
        ├── java/com/psbc/mock/
        │   ├── MockServiceTest.java           # 基础测试类
        │   └── client/
        │       └── MockServiceTestClient.java # 测试客户端
        └── resources/
            └── application-test.yml           # 测试环境配置
```

## 模拟的服务端点

### 1. 综合办公系统 HTTP 服务 (端口: 9091)

根据配置文件 `local/application.yml` 中的配置：
- `zhbg.zhbgPostUrl: http://127.0.0.1:9091/`
- `zhbg.url30001: api/admin/xzp/queryYxjfLsxdye`
- `zhbg.url20006: api/admin/xzp/queryJgzhInfo`

#### 接口详情：
- **30001接口**: `POST /api/admin/xzp/queryYxjfLsxdye` (JSON格式)
- **20006接口**: `POST /api/admin/xzp/queryJgzhInfo` (XML格式)
- **健康检查**: `GET /api/admin/xzp/health`

### 2. 委托方 WebService 服务 (端口: 9091)

根据配置文件中的配置：
- `webservice.client.url: https://127.0.0.1:8443/xmysfzjjg/wservices/IWebServiceService?wsdl`
- `cxf.path: /xmysfzjjg/wservices`

#### 接口详情：
- **WSDL地址**: `http://localhost:9091/xmysfzjjg/wservices/IWebServiceService?wsdl`
- **方法**: `Execute(String bankId, String inParmeter)`
- **命名空间**: `http://tempuri.org/`

### 3. 外联系统 Socket 服务 (端口: 9702)

根据配置文件中的配置：
- `xmysfzjjg.PORT: 9702`

#### 接口详情：
- **协议**: 6位长度前缀 + 消息体
- **编码**: UTF-8
- **消息格式**: XML

## 技术栈

- **Spring Boot 2.7.18**: 主框架
- **Apache CXF 3.5.5**: WebService框架
- **Netty 4.1.100**: Socket通信框架
- **Maven**: 项目构建工具
- **Lombok**: 代码简化工具
- **JUnit 5**: 单元测试框架

## 核心功能

### 1. HTTP接口模拟 (ZhbgController)
- 模拟综合办公系统的30001和20006接口
- 支持JSON和XML格式的请求和响应
- 提供详细的请求和响应日志
- 包含健康检查接口

### 2. WebService接口模拟 (MockWebServiceImpl)
- 完全兼容原项目的ClientIWebService接口
- 支持CXF框架的WebService调用
- 模拟真实的业务处理时间
- 返回符合格式的XML响应

### 3. Socket服务模拟 (MockSocketServer)
- 使用Netty框架实现高性能Socket服务
- 支持6位长度前缀的自定义协议
- 自动编解码消息
- 模拟外联系统的响应格式

## 配置说明

### 应用配置 (application.yml)
```yaml
server:
  port: 9091  # HTTP服务端口

mock:
  zhbg:
    enabled: true     # 启用综合办公系统模拟
  webservice:
    enabled: true     # 启用WebService模拟
  socket:
    port: 9702
    enabled: true     # 启用Socket服务模拟

cxf:
  path: /xmysfzjjg/wservices  # WebService路径
```

## 使用方法

### 1. 快速启动
```bash
# Windows
start.bat

# 或使用Maven
mvn spring-boot:run
```

### 2. 测试验证
```bash
# 运行测试客户端
mvn test-compile exec:java -Dexec.mainClass="com.psbc.mock.client.MockServiceTestClient"
```

### 3. 手动测试
```bash
# 测试30001接口
curl -X POST http://localhost:9091/api/admin/xzp/queryYxjfLsxdye \
  -H "Content-Type: application/json" \
  -d '{"serviceno":"30001","data":"test"}'

# 测试20006接口
curl -X POST http://localhost:9091/api/admin/xzp/queryJgzhInfo \
  -H "Content-Type: application/xml" \
  -d '<?xml version="1.0"?><content><head><serviceno>20006</serviceno></head></content>'

# 测试健康检查
curl http://localhost:9091/api/admin/xzp/health
```

## 与中转服务的对接

### 1. 配置中转服务
确保中转服务的 `local/application.yml` 配置指向挡板服务：
```yaml
zhbg:
  zhbgPostUrl: http://127.0.0.1:9091/

webservice:
  client:
    url: http://127.0.0.1:9091/xmysfzjjg/wservices/IWebServiceService?wsdl

xmysfzjjg:
  IP: 127.0.0.1
  PORT: 9702
```

### 2. 启动顺序
1. 先启动挡板服务 (mock-work)
2. 再启动中转服务 (branch-agent)
3. 使用NettySocketClient测试连通性

### 3. 测试流程
1. 中转服务接收Socket请求
2. 根据服务号路由到不同的下游系统
3. 挡板服务返回模拟响应
4. 验证整个链路的连通性

## 日志和监控

### 1. 日志级别
- INFO: 请求和响应信息
- DEBUG: 详细的处理过程
- ERROR: 错误信息

### 2. 关键日志
- 接收到的请求内容
- 返回的响应内容
- 处理时间统计
- 连接建立和断开

### 3. 监控指标
- 请求数量统计
- 响应时间统计
- 错误率统计
- 连接数统计

## 扩展和定制

### 1. 添加新的HTTP接口
在 `ZhbgController` 中添加新的 `@PostMapping` 方法

### 2. 修改WebService响应
在 `MockWebServiceImpl.Execute()` 方法中修改响应逻辑

### 3. 自定义Socket协议
修改 `MockSocketDecoder` 和 `MockSocketEncoder` 类

### 4. 添加业务逻辑
在各个处理器中添加更复杂的业务逻辑模拟

## 部署说明

### 1. 开发环境
- 直接使用 `mvn spring-boot:run` 启动
- 支持热重载和调试

### 2. 测试环境
- 使用 `mvn clean package` 打包
- 使用 `java -jar target/mock-service-1.0.0.jar` 运行

### 3. 生产环境
- 配置外部配置文件
- 使用Docker容器化部署
- 配置日志收集和监控

## 注意事项

1. **端口冲突**: 确保9091和9702端口未被占用
2. **防火墙**: 确保防火墙允许相应端口的访问
3. **编码格式**: 所有消息使用UTF-8编码
4. **协议兼容**: Socket协议必须与中转服务保持一致
5. **响应格式**: 响应格式必须符合中转服务的解析要求

## 故障排除

### 1. 启动失败
- 检查Java和Maven环境
- 检查端口占用情况
- 查看启动日志错误信息

### 2. 连接失败
- 检查网络连通性
- 确认服务已正常启动
- 检查防火墙设置

### 3. 响应异常
- 检查请求格式是否正确
- 查看服务端日志
- 验证协议兼容性

## 总结

本挡板测试服务完全按照中转服务的配置文件要求设计，提供了完整的下游系统模拟功能，包括：

✅ **综合办公系统HTTP接口** - 完全模拟30001和20006服务  
✅ **委托方WebService接口** - 完全兼容ClientIWebService  
✅ **外联系统Socket接口** - 完全支持6位长度前缀协议  
✅ **完整的测试工具** - 提供测试客户端和验证脚本  
✅ **详细的文档** - 包含使用说明和故障排除指南  

该服务可以有效验证中转服务的各个接口连通性，为系统集成测试提供可靠的支撑。
