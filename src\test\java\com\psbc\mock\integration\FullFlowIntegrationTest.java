package com.psbc.mock.integration;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 全流程集成测试
 * 测试整个挡板服务的各个接口和场景
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles("test")
public class FullFlowIntegrationTest {

    private static final String BASE_URL = "http://localhost:9091";
    private static final String SOCKET_HOST = "localhost";
    private static final int SOCKET_PORT = 9702;

    @BeforeEach
    void setUp() throws InterruptedException {
        // 等待服务启动
        Thread.sleep(2000);
    }

    @Test
    void testHealthCheck() throws Exception {
        log.info("测试健康检查接口...");
        
        String response = sendGetRequest(BASE_URL + "/api/admin/xzp/health");
        assertNotNull(response);
        assertTrue(response.contains("UP"));
        
        log.info("健康检查测试通过");
    }

    @Test
    void test30001Interface() throws Exception {
        log.info("测试30001接口...");
        
        String requestBody = "{\n" +
                "  \"serviceno\": \"30001\",\n" +
                "  \"data\": {\n" +
                "    \"customerName\": \"张三\",\n" +
                "    \"idCard\": \"350200199001010001\",\n" +
                "    \"loanAmount\": \"500000.00\"\n" +
                "  }\n" +
                "}";
        
        String response = sendPostRequest(BASE_URL + "/api/admin/xzp/queryYxjfLsxdye", 
                                        requestBody, "application/json");
        
        assertNotNull(response);
        assertTrue(response.contains("success") || response.contains("0000"));
        
        log.info("30001接口测试通过");
    }

    @Test
    void test20006Interface() throws Exception {
        log.info("测试20006接口...");
        
        String requestBody = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <serviceno>20006</serviceno>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <accountQuery>\n" +
                "      <accountNo>6217000010001234567</accountNo>\n" +
                "    </accountQuery>\n" +
                "  </body>\n" +
                "</content>";
        
        String response = sendPostRequest(BASE_URL + "/api/admin/xzp/queryJgzhInfo", 
                                        requestBody, "application/xml");
        
        assertNotNull(response);
        assertTrue(response.contains("statecode") && response.contains("1"));
        
        log.info("20006接口测试通过");
    }

    @Test
    void testSocketInterface() throws Exception {
        log.info("测试Socket接口...");
        
        String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <serviceno>99999</serviceno>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <transaction>\n" +
                "      <amount>10000.00</amount>\n" +
                "      <accountNo>6217000010001234567</accountNo>\n" +
                "    </transaction>\n" +
                "  </body>\n" +
                "</content>";
        
        String response = sendSocketMessage(message);
        
        assertNotNull(response);
        assertTrue(response.contains("statecode") && response.contains("1"));
        
        log.info("Socket接口测试通过");
    }

    @Test
    void testErrorScenarios() throws Exception {
        log.info("测试错误场景...");
        
        // 测试30001错误场景
        String errorRequest = "{\n" +
                "  \"serviceno\": \"30001\",\n" +
                "  \"data\": \"error\"\n" +
                "}";
        
        String response = sendPostRequest(BASE_URL + "/api/admin/xzp/queryYxjfLsxdye", 
                                        errorRequest, "application/json");
        
        assertNotNull(response);
        // 错误响应应该包含错误信息
        assertTrue(response.contains("error") || response.contains("9999"));
        
        log.info("错误场景测试通过");
    }

    @Test
    void testConcurrentRequests() throws Exception {
        log.info("测试并发请求...");
        
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CompletableFuture<String>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                try {
                    String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                            "<content>\n" +
                            "  <head>\n" +
                            "    <serviceno>99999</serviceno>\n" +
                            "  </head>\n" +
                            "  <body>\n" +
                            "    <threadId>" + threadId + "</threadId>\n" +
                            "  </body>\n" +
                            "</content>";
                    
                    return sendSocketMessage(message);
                } catch (Exception e) {
                    log.error("并发请求失败", e);
                    return null;
                }
            }, executor);
        }
        
        // 等待所有请求完成
        CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
        
        // 验证所有响应
        for (CompletableFuture<String> future : futures) {
            String response = future.get();
            assertNotNull(response);
            assertTrue(response.contains("statecode"));
        }
        
        executor.shutdown();
        log.info("并发请求测试通过");
    }

    @Test
    void testConfigurationAPI() throws Exception {
        log.info("测试配置管理API...");
        
        // 测试获取服务配置
        String configResponse = sendGetRequest(BASE_URL + "/api/config/services");
        assertNotNull(configResponse);
        assertTrue(configResponse.contains("ZHBG") || configResponse.contains("WEBSERVICE"));
        
        // 测试设置延迟
        String delayResponse = sendPostRequest(BASE_URL + "/api/config/delay/200", "", "application/json");
        assertNotNull(delayResponse);
        assertTrue(delayResponse.contains("成功") || delayResponse.contains("success"));
        
        // 测试重置配置
        String resetResponse = sendPostRequest(BASE_URL + "/api/config/reset", "", "application/json");
        assertNotNull(resetResponse);
        assertTrue(resetResponse.contains("成功") || resetResponse.contains("success"));
        
        log.info("配置管理API测试通过");
    }

    @Test
    void testServiceStatus() throws Exception {
        log.info("测试服务状态接口...");
        
        String response = sendGetRequest(BASE_URL + "/api/admin/xzp/status");
        assertNotNull(response);
        assertTrue(response.contains("30001") && response.contains("20006"));
        
        log.info("服务状态接口测试通过");
    }

    @Test
    void testResponseTimeouts() throws Exception {
        log.info("测试响应超时场景...");
        
        // 设置较短的超时时间进行测试
        String timeoutMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <serviceno>99999</serviceno>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <testData>timeout</testData>\n" +
                "  </body>\n" +
                "</content>";
        
        long startTime = System.currentTimeMillis();
        String response = sendSocketMessage(timeoutMessage);
        long endTime = System.currentTimeMillis();
        
        assertNotNull(response);
        // 超时测试应该花费更长时间
        assertTrue((endTime - startTime) > 1000);
        
        log.info("响应超时场景测试通过");
    }

    @Test
    void testDifferentServiceNumbers() throws Exception {
        log.info("测试不同服务号的处理...");
        
        String[] serviceNumbers = {"30001", "20006", "99999", "12345"};
        
        for (String serviceNo : serviceNumbers) {
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>" + serviceNo + "</serviceno>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <testData>service test</testData>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            assertNotNull(response);
            assertTrue(response.contains("statecode"));
            
            log.info("服务号 {} 测试通过", serviceNo);
        }
        
        log.info("不同服务号处理测试通过");
    }

    // 辅助方法
    private String sendGetRequest(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.connect();
        
        return readResponse(conn);
    }

    private String sendPostRequest(String urlStr, String requestBody, String contentType) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", contentType);
        conn.connect();
        
        if (!requestBody.isEmpty()) {
            try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8)) {
                out.write(requestBody);
                out.flush();
            }
        }
        
        return readResponse(conn);
    }

    private String readResponse(HttpURLConnection conn) throws Exception {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    private String sendSocketMessage(String message) throws Exception {
        try (Socket socket = new Socket(SOCKET_HOST, SOCKET_PORT)) {
            sendFormattedMessage(socket, message);
            return receiveFormattedMessage(socket);
        }
    }

    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        try (OutputStream out = socket.getOutputStream()) {
            out.write(lengthBytes);
            out.write(messageBytes);
            out.flush();
        }
    }

    private String receiveFormattedMessage(Socket socket) throws IOException {
        try (InputStream in = socket.getInputStream()) {
            byte[] lengthBytes = new byte[6];
            int bytesRead = in.read(lengthBytes);
            if (bytesRead != 6) {
                throw new IOException("无法读取完整的长度字段");
            }
            
            String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
            int messageLength = Integer.parseInt(lengthStr);
            
            byte[] messageBytes = new byte[messageLength];
            int totalBytesRead = 0;
            while (totalBytesRead < messageLength) {
                int currentBytesRead = in.read(messageBytes, totalBytesRead, messageLength - totalBytesRead);
                if (currentBytesRead == -1) {
                    throw new IOException("连接意外关闭");
                }
                totalBytesRead += currentBytesRead;
            }
            
            return new String(messageBytes, StandardCharsets.UTF_8);
        }
    }
}
