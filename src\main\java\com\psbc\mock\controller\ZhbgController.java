package com.psbc.mock.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 综合办公系统HTTP接口模拟
 * 模拟服务号30001和20006的响应，支持多种测试场景
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/xzp")
public class ZhbgController {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${mock.zhbg.response-delay:100}")
    private int responseDelay;

    @Value("${mock.zhbg.error-rate:0}")
    private int errorRate;

    /**
     * 模拟服务号30001接口 - 查询按揭贷款信息
     * 接收JSON格式请求，返回JSON响应
     * 支持多种测试场景：成功、失败、异常等
     */
    @PostMapping(value = "/queryYxjfLsxdye",
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> queryYxjfLsxdye(@RequestBody String request) {
        long startTime = System.currentTimeMillis();
        log.info("收到30001接口请求: {}", request);

        try {
            // 模拟处理延迟
            if (responseDelay > 0) {
                Thread.sleep(responseDelay);
            }

            // 解析请求参数
            JsonNode requestNode = objectMapper.readTree(request);
            String serviceno = requestNode.path("serviceno").asText();
            String data = requestNode.path("data").asText();

            // 根据请求内容决定响应类型
            String response = generateResponse30001(data);

            long endTime = System.currentTimeMillis();
            log.info("30001接口处理耗时: {}ms, 响应: {}", (endTime - startTime), response);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("处理30001接口请求异常", e);
            String errorResponse = generateErrorResponse30001("系统异常: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 生成30001接口的响应
     */
    private String generateResponse30001(String data) {
        // 根据请求数据模拟不同场景
        if (data.contains("error")) {
            return generateErrorResponse30001("业务处理失败");
        }

        if (data.contains("timeout")) {
            try {
                Thread.sleep(5000); // 模拟超时
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 随机错误模拟
        if (errorRate > 0 && ThreadLocalRandom.current().nextInt(100) < errorRate) {
            return generateErrorResponse30001("随机错误模拟");
        }

        // 成功响应
        return "{\n" +
                "  \"status\": \"success\",\n" +
                "  \"code\": \"0000\",\n" +
                "  \"message\": \"查询成功\",\n" +
                "  \"data\": {\n" +
                "    \"loanInfo\": {\n" +
                "      \"loanId\": \"LOAN" + System.currentTimeMillis() + "\",\n" +
                "      \"amount\": \"" + ThreadLocalRandom.current().nextInt(100000, 1000000) + ".00\",\n" +
                "      \"status\": \"APPROVED\",\n" +
                "      \"approveDate\": \"" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "\",\n" +
                "      \"customerName\": \"测试客户\",\n" +
                "      \"idCard\": \"350200199001010001\",\n" +
                "      \"bankAccount\": \"6217000010001234567\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"timestamp\": \"" + System.currentTimeMillis() + "\"\n" +
                "}";
    }

    /**
     * 生成30001接口的错误响应
     */
    private String generateErrorResponse30001(String errorMsg) {
        return "{\n" +
                "  \"status\": \"error\",\n" +
                "  \"code\": \"9999\",\n" +
                "  \"message\": \"" + errorMsg + "\",\n" +
                "  \"data\": null,\n" +
                "  \"timestamp\": \"" + System.currentTimeMillis() + "\"\n" +
                "}";
    }

    /**
     * 模拟服务号20006接口 - 查询监管账户变动反馈信息
     * 接收XML格式请求，返回XML响应
     * 支持多种测试场景：成功、失败、异常等
     */
    @PostMapping(value = "/queryJgzhInfo",
                 consumes = MediaType.APPLICATION_XML_VALUE,
                 produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> queryJgzhInfo(@RequestBody String request) {
        long startTime = System.currentTimeMillis();
        log.info("收到20006接口请求: {}", request);

        try {
            // 模拟处理延迟
            if (responseDelay > 0) {
                Thread.sleep(responseDelay);
            }

            // 解析XML请求
            String serviceno = extractXmlValue(request, "serviceno");
            String data = extractXmlValue(request, "data");

            // 根据请求内容决定响应类型
            String response = generateResponse20006(data);

            long endTime = System.currentTimeMillis();
            log.info("20006接口处理耗时: {}ms, 响应: {}", (endTime - startTime), response);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("处理20006接口请求异常", e);
            String errorResponse = generateErrorResponse20006("系统异常: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 生成20006接口的响应
     */
    private String generateResponse20006(String data) {
        // 根据请求数据模拟不同场景
        if (data != null && data.contains("error")) {
            return generateErrorResponse20006("业务处理失败");
        }

        if (data != null && data.contains("timeout")) {
            try {
                Thread.sleep(5000); // 模拟超时
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 随机错误模拟
        if (errorRate > 0 && ThreadLocalRandom.current().nextInt(100) < errorRate) {
            return generateErrorResponse20006("随机错误模拟");
        }

        // 成功响应
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>查询成功</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <accountInfo>\n" +
                "      <accountNo>6217000010001234567</accountNo>\n" +
                "      <ismatch>0</ismatch>\n" +
                "      <balance>" + ThreadLocalRandom.current().nextInt(100000, ********) + ".00</balance>\n" +
                "      <status>ACTIVE</status>\n" +
                "      <lastUpdateTime>" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "</lastUpdateTime>\n" +
                "      <customerName>测试客户</customerName>\n" +
                "      <idCard>350200199001010001</idCard>\n" +
                "    </accountInfo>\n" +
                "  </body>\n" +
                "</response>";
    }

    /**
     * 生成20006接口的错误响应
     */
    private String generateErrorResponse20006(String errorMsg) {
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <head>\n" +
                "    <statecode>0</statecode>\n" +
                "    <msg>" + errorMsg + "</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <error>\n" +
                "      <code>9999</code>\n" +
                "      <message>" + errorMsg + "</message>\n" +
                "    </error>\n" +
                "  </body>\n" +
                "</response>";
    }

    /**
     * 从XML中提取指定标签的值
     */
    private String extractXmlValue(String xml, String tagName) {
        try {
            String startTag = "<" + tagName + ">";
            String endTag = "</" + tagName + ">";
            int startIndex = xml.indexOf(startTag);
            int endIndex = xml.indexOf(endTag);

            if (startIndex != -1 && endIndex != -1) {
                return xml.substring(startIndex + startTag.length(), endIndex);
            }
        } catch (Exception e) {
            log.warn("解析XML标签{}失败: {}", tagName, e.getMessage());
        }
        return null;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        String response = "{\n" +
                "  \"status\": \"UP\",\n" +
                "  \"service\": \"zhbg-mock\",\n" +
                "  \"timestamp\": \"" + System.currentTimeMillis() + "\",\n" +
                "  \"version\": \"1.0.0\",\n" +
                "  \"uptime\": \"" + (System.currentTimeMillis() / 1000) + "s\"\n" +
                "}";
        return ResponseEntity.ok(response);
    }

    /**
     * 获取服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<String> status() {
        String response = "{\n" +
                "  \"zhbg\": {\n" +
                "    \"30001\": {\n" +
                "      \"name\": \"查询按揭贷款信息\",\n" +
                "      \"status\": \"ACTIVE\",\n" +
                "      \"responseDelay\": " + responseDelay + ",\n" +
                "      \"errorRate\": " + errorRate + "\n" +
                "    },\n" +
                "    \"20006\": {\n" +
                "      \"name\": \"查询监管账户变动反馈信息\",\n" +
                "      \"status\": \"ACTIVE\",\n" +
                "      \"responseDelay\": " + responseDelay + ",\n" +
                "      \"errorRate\": " + errorRate + "\n" +
                "    }\n" +
                "  },\n" +
                "  \"timestamp\": \"" + System.currentTimeMillis() + "\"\n" +
                "}";
        return ResponseEntity.ok(response);
    }

    /**
     * 动态配置响应延迟
     */
    @PostMapping("/config/delay/{delay}")
    public ResponseEntity<String> setResponseDelay(@PathVariable int delay) {
        this.responseDelay = delay;
        log.info("设置响应延迟为: {}ms", delay);
        return ResponseEntity.ok("{\"message\":\"响应延迟已设置为" + delay + "ms\"}");
    }

    /**
     * 动态配置错误率
     */
    @PostMapping("/config/error-rate/{rate}")
    public ResponseEntity<String> setErrorRate(@PathVariable int rate) {
        if (rate < 0 || rate > 100) {
            return ResponseEntity.badRequest().body("{\"error\":\"错误率必须在0-100之间\"}");
        }
        this.errorRate = rate;
        log.info("设置错误率为: {}%", rate);
        return ResponseEntity.ok("{\"message\":\"错误率已设置为" + rate + "%\"}");
    }
}
