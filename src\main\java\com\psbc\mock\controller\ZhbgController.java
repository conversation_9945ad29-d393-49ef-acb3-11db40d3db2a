package com.psbc.mock.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 综合办公系统HTTP接口模拟
 * 模拟服务号30001和20006的响应
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/xzp")
public class ZhbgController {

    /**
     * 模拟服务号30001接口 - 查询按揭贷款信息
     * 接收JSON格式请求，返回JSON响应
     */
    @PostMapping(value = "/queryYxjfLsxdye", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    public String queryYxjfLsxdye(@RequestBody String request) {
        log.info("收到30001接口请求: {}", request);
        
        // 模拟成功响应
        String response = "{\n" +
                "  \"status\": \"success\",\n" +
                "  \"code\": \"0000\",\n" +
                "  \"message\": \"查询成功\",\n" +
                "  \"data\": {\n" +
                "    \"loanInfo\": {\n" +
                "      \"loanId\": \"LOAN20240701001\",\n" +
                "      \"amount\": \"500000.00\",\n" +
                "      \"status\": \"APPROVED\",\n" +
                "      \"approveDate\": \"2024-07-01\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"timestamp\": \"" + System.currentTimeMillis() + "\"\n" +
                "}";
        
        log.info("返回30001接口响应: {}", response);
        return response;
    }

    /**
     * 模拟服务号20006接口 - 查询监管账户变动反馈信息
     * 接收XML格式请求，返回XML响应
     */
    @PostMapping(value = "/queryJgzhInfo", 
                 consumes = MediaType.APPLICATION_XML_VALUE,
                 produces = MediaType.APPLICATION_XML_VALUE)
    public String queryJgzhInfo(@RequestBody String request) {
        log.info("收到20006接口请求: {}", request);
        
        // 模拟成功响应
        String response = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>查询成功</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <accountInfo>\n" +
                "      <accountNo>6217000010001234567</accountNo>\n" +
                "      <ismatch>0</ismatch>"+
                "      <balance>1000000.00</balance>\n" +
                "      <status>ACTIVE</status>\n" +
                "      <lastUpdateTime>2024-07-01 14:30:00</lastUpdateTime>\n" +
                "    </accountInfo>\n" +
                "  </body>\n" +
                "</response>";
        
        log.info("返回20006接口响应: {}", response);
        return response;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public String health() {
        return "{\"status\":\"UP\",\"service\":\"zhbg-mock\"}";
    }
}
