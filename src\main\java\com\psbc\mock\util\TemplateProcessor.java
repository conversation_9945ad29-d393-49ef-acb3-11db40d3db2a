package com.psbc.mock.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板处理工具类
 * 用于处理响应模板中的变量替换
 */
@Slf4j
@Component
public class TemplateProcessor {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    /**
     * 处理模板，替换其中的变量
     */
    public String processTemplate(String template, Map<String, Object> variables) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        // 合并默认变量和自定义变量
        Map<String, Object> allVariables = new HashMap<>();
        allVariables.putAll(getDefaultVariables());
        if (variables != null) {
            allVariables.putAll(variables);
        }

        // 替换模板中的变量
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = allVariables.get(variableName);
            
            if (value != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(value.toString()));
            } else {
                // 如果变量不存在，尝试生成动态值
                String dynamicValue = generateDynamicValue(variableName);
                if (dynamicValue != null) {
                    matcher.appendReplacement(result, Matcher.quoteReplacement(dynamicValue));
                } else {
                    // 保留原始变量
                    matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group(0)));
                    log.warn("未找到变量: {}", variableName);
                }
            }
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 获取默认变量
     */
    private Map<String, Object> getDefaultVariables() {
        Map<String, Object> variables = new HashMap<>();
        
        long timestamp = System.currentTimeMillis();
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        
        // 时间相关变量
        variables.put("timestamp", timestamp);
        variables.put("currentDateTime", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        variables.put("currentDate", today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        variables.put("currentTime", now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        variables.put("currentYear", String.valueOf(now.getYear()));
        variables.put("currentMonth", String.valueOf(now.getMonthValue()));
        variables.put("currentDay", String.valueOf(now.getDayOfMonth()));
        
        // 随机数相关变量
        variables.put("randomAmount", String.format("%.2f", ThreadLocalRandom.current().nextDouble(1000, 100000)));
        variables.put("randomBalance", String.format("%.2f", ThreadLocalRandom.current().nextDouble(100000, ********)));
        variables.put("randomInt", ThreadLocalRandom.current().nextInt(1000, 9999));
        variables.put("randomLong", ThreadLocalRandom.current().nextLong(********0L, ********9L));
        
        // 业务相关变量
        variables.put("defaultAccountNo", "6217000010001234567");
        variables.put("defaultCustomerName", "测试客户");
        variables.put("defaultIdCard", "**************0001");
        variables.put("defaultBankId", "YCYH");
        variables.put("defaultCurrency", "CNY");
        
        return variables;
    }

    /**
     * 生成动态值
     */
    private String generateDynamicValue(String variableName) {
        switch (variableName.toLowerCase()) {
            case "uuid":
                return java.util.UUID.randomUUID().toString();
            case "randomuuid":
                return java.util.UUID.randomUUID().toString().replace("-", "");
            case "randomphone":
                return "138" + String.format("%08d", ThreadLocalRandom.current().nextInt(********, ********));
            case "randomaccountno":
                return "6217" + String.format("%015d", ThreadLocalRandom.current().nextLong(********0000000L, ********9999999L));
            case "randomidcard":
                return "**************" + String.format("%04d", ThreadLocalRandom.current().nextInt(1000, 9999));
            case "randomloanid":
                return "LOAN" + System.currentTimeMillis();
            case "randomtransactionid":
                return "TXN" + System.currentTimeMillis();
            case "randominstructionno":
                return "INST" + System.currentTimeMillis();
            case "randomreferenceno":
                return "REF" + System.currentTimeMillis();
            default:
                return null;
        }
    }

    /**
     * 处理模板（使用默认变量）
     */
    public String processTemplate(String template) {
        return processTemplate(template, null);
    }

    /**
     * 创建变量映射
     */
    public Map<String, Object> createVariables() {
        return new HashMap<>();
    }

    /**
     * 添加变量
     */
    public Map<String, Object> addVariable(Map<String, Object> variables, String name, Object value) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        variables.put(name, value);
        return variables;
    }

    /**
     * 批量添加变量
     */
    public Map<String, Object> addVariables(Map<String, Object> variables, Map<String, Object> newVariables) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        if (newVariables != null) {
            variables.putAll(newVariables);
        }
        return variables;
    }

    /**
     * 获取支持的变量列表
     */
    public Map<String, String> getSupportedVariables() {
        Map<String, String> variables = new HashMap<>();
        
        // 时间变量
        variables.put("timestamp", "当前时间戳");
        variables.put("currentDateTime", "当前日期时间 (yyyy-MM-dd HH:mm:ss)");
        variables.put("currentDate", "当前日期 (yyyy-MM-dd)");
        variables.put("currentTime", "当前时间 (HH:mm:ss)");
        variables.put("currentYear", "当前年份");
        variables.put("currentMonth", "当前月份");
        variables.put("currentDay", "当前日期");
        
        // 随机数变量
        variables.put("randomAmount", "随机金额 (1000-100000)");
        variables.put("randomBalance", "随机余额 (100000-********)");
        variables.put("randomInt", "随机整数 (1000-9999)");
        variables.put("randomLong", "随机长整数");
        variables.put("uuid", "UUID");
        variables.put("randomUUID", "无连字符的UUID");
        variables.put("randomPhone", "随机手机号");
        variables.put("randomAccountNo", "随机账号");
        variables.put("randomIdCard", "随机身份证号");
        variables.put("randomLoanId", "随机贷款编号");
        variables.put("randomTransactionId", "随机交易编号");
        variables.put("randomInstructionNo", "随机指令编号");
        variables.put("randomReferenceNo", "随机参考编号");
        
        // 默认业务变量
        variables.put("defaultAccountNo", "默认账号");
        variables.put("defaultCustomerName", "默认客户姓名");
        variables.put("defaultIdCard", "默认身份证号");
        variables.put("defaultBankId", "默认银行代码");
        variables.put("defaultCurrency", "默认货币");
        
        return variables;
    }
}
