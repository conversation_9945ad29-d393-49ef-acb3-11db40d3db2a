package com.psbc.mock.socket;

import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 外联系统Socket消息处理器
 * 处理来自中转服务的Socket请求，支持多种业务场景
 */
@Slf4j
@Sharable
@Component
public class MockSocketHandler extends ChannelInboundHandlerAdapter {

    @Value("${mock.socket.response-delay:50}")
    private int responseDelay;

    @Value("${mock.socket.error-rate:0}")
    private int errorRate;

    private final AtomicLong connectionCounter = new AtomicLong(0);
    private final AtomicLong messageCounter = new AtomicLong(0);

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        long connectionId = connectionCounter.incrementAndGet();
        log.info("外联系统Socket连接建立：{}, 连接ID: {}, 当前连接数: {}",
                ctx.channel().remoteAddress(), connectionId, connectionCounter.get());

        // 将连接ID存储到channel属性中
        ctx.channel().attr(io.netty.util.AttributeKey.valueOf("connectionId")).set(connectionId);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        long messageId = messageCounter.incrementAndGet();
        long startTime = System.currentTimeMillis();

        try {
            String message = (String) msg;
            log.info("外联系统收到消息 [{}]: {}", messageId, message);

            // 模拟处理时间
            if (responseDelay > 0) {
                Thread.sleep(responseDelay);
            }

            // 生成响应消息
            String response = generateResponse(message, messageId);

            // 发送响应
            ctx.writeAndFlush(response);

            long endTime = System.currentTimeMillis();
            log.info("外联系统消息处理完成 [{}], 耗时: {}ms", messageId, (endTime - startTime));

        } catch (Exception e) {
            log.error("外联系统处理消息 [{}] 时发生错误", messageId, e);
            // 发送错误响应
            String errorResponse = generateErrorResponse("处理失败: " + e.getMessage(), messageId);
            ctx.writeAndFlush(errorResponse);
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        Long connectionId = (Long) ctx.channel().attr(io.netty.util.AttributeKey.valueOf("connectionId")).get();
        log.error("外联系统Socket连接异常 [连接ID: {}]", connectionId, cause);
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Long connectionId = (Long) ctx.channel().attr(io.netty.util.AttributeKey.valueOf("connectionId")).get();
        connectionCounter.decrementAndGet();
        log.info("外联系统Socket连接断开：{}, 连接ID: {}, 剩余连接数: {}",
                ctx.channel().remoteAddress(), connectionId, connectionCounter.get());
    }

    /**
     * 根据请求消息生成响应
     */
    private String generateResponse(String request, long messageId) {
        // 解析服务号
        String serviceno = extractServiceNo(request);

        // 检查是否需要模拟错误
        if (request.contains("error")) {
            return generateErrorResponse("业务处理失败", messageId);
        }

        // 检查是否需要模拟超时
        if (request.contains("timeout")) {
            try {
                Thread.sleep(5000); // 模拟超时
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 随机错误模拟
        if (errorRate > 0 && ThreadLocalRandom.current().nextInt(100) < errorRate) {
            return generateErrorResponse("随机错误模拟", messageId);
        }

        // 根据服务号生成不同的响应
        return generateSuccessResponse(serviceno, request, messageId);
    }

    /**
     * 生成成功响应
     */
    private String generateSuccessResponse(String serviceno, String request, long messageId) {
        String instructionno = "MOCK" + System.currentTimeMillis();
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 根据不同的服务号返回不同的业务数据
        String businessData = generateBusinessDataByServiceNo(serviceno, messageId);

        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>交易成功</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "    <serviceno>" + serviceno + "</serviceno>\n" +
                "    <messageId>" + messageId + "</messageId>\n" +
                "  </head>\n" +
                "  <body>\n" +
                businessData +
                "  </body>\n" +
                "</content>";
    }

    /**
     * 生成错误响应
     */
    private String generateErrorResponse(String errorMsg, long messageId) {
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <statecode>0</statecode>\n" +
                "    <msg>" + errorMsg + "</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "    <messageId>" + messageId + "</messageId>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <error>\n" +
                "      <code>9999</code>\n" +
                "      <message>" + errorMsg + "</message>\n" +
                "    </error>\n" +
                "  </body>\n" +
                "</content>";
    }

    /**
     * 从请求中提取服务号
     */
    private String extractServiceNo(String request) {
        try {
            String startTag = "<serviceno>";
            String endTag = "</serviceno>";
            int startIndex = request.indexOf(startTag);
            int endIndex = request.indexOf(endTag);

            if (startIndex != -1 && endIndex != -1) {
                return request.substring(startIndex + startTag.length(), endIndex);
            }
        } catch (Exception e) {
            log.warn("解析服务号失败: {}", e.getMessage());
        }
        return "99999"; // 默认服务号
    }

    /**
     * 根据服务号生成业务数据
     */
    private String generateBusinessDataByServiceNo(String serviceno, long messageId) {
        StringBuilder businessData = new StringBuilder();

        switch (serviceno) {
            case "30001":
                // 查询按揭贷款信息
                businessData.append("    <loanInfo>\n")
                           .append("      <loanId>LOAN").append(System.currentTimeMillis()).append("</loanId>\n")
                           .append("      <amount>").append(ThreadLocalRandom.current().nextInt(100000, 1000000)).append(".00</amount>\n")
                           .append("      <status>APPROVED</status>\n")
                           .append("      <customerName>测试客户</customerName>\n")
                           .append("      <idCard>350200199001010001</idCard>\n")
                           .append("    </loanInfo>\n");
                break;

            case "20006":
                // 查询监管账户变动反馈信息
                businessData.append("    <accountInfo>\n")
                           .append("      <accountNo>6217000010001234567</accountNo>\n")
                           .append("      <balance>").append(ThreadLocalRandom.current().nextInt(100000, ********)).append(".00</balance>\n")
                           .append("      <status>ACTIVE</status>\n")
                           .append("      <lastUpdateTime>").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("</lastUpdateTime>\n")
                           .append("    </accountInfo>\n");
                break;

            default:
                // 默认账户交易信息
                businessData.append("    <table_account>\n")
                           .append("      <row>\n")
                           .append("        <instructionno>MOCK").append(System.currentTimeMillis()).append("</instructionno>\n")
                           .append("        <issuccess>1</issuccess>\n")
                           .append("        <amount>").append(ThreadLocalRandom.current().nextInt(1000, 100000)).append(".00</amount>\n")
                           .append("        <balance>").append(ThreadLocalRandom.current().nextInt(100000, ********)).append(".99</balance>\n")
                           .append("        <transactionTime>").append(System.currentTimeMillis()).append("</transactionTime>\n")
                           .append("        <messageId>").append(messageId).append("</messageId>\n")
                           .append("      </row>\n")
                           .append("    </table_account>\n");
                break;
        }

        return businessData.toString();
    }
}
