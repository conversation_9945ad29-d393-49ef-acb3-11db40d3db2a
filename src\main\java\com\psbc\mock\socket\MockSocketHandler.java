package com.psbc.mock.socket;

import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * 外联系统Socket消息处理器
 * 处理来自中转服务的Socket请求
 */
@Slf4j
@Sharable
public class MockSocketHandler extends ChannelInboundHandlerAdapter {

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("外联系统Socket连接建立：{}", ctx.channel().remoteAddress());
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        try {
            String message = (String) msg;
            log.info("外联系统收到消息：{}", message);
            
            // 模拟处理时间
            Thread.sleep(50);
            
            // 生成响应消息
            String response = generateResponse(message);
            
            // 发送响应
            ctx.writeAndFlush(response);
            log.info("外联系统发送响应完成");
            
        } catch (Exception e) {
            log.error("外联系统处理消息时发生错误", e);
            // 发送错误响应
            String errorResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><statecode>0</statecode><msg>处理失败</msg></head></content>";
            ctx.writeAndFlush(errorResponse);
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("外联系统Socket连接异常", cause);
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.info("外联系统Socket连接断开：{}", ctx.channel().remoteAddress());
    }

    /**
     * 根据请求消息生成响应
     */
    private String generateResponse(String request) {
        // 模拟外联系统的响应格式
        String response = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>交易成功</msg>\n" +
                "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <table_account>\n" +
                "      <row>\n" +
                "        <instructionno>MOCK" + System.currentTimeMillis() + "</instructionno>\n" +
                "        <issuccess>1</issuccess>\n" +
                "        <amount>100000.00</amount>\n" +
                "        <balance>999999.99</balance>\n" +
                "        <transactionTime>" + System.currentTimeMillis() + "</transactionTime>\n" +
                "      </row>\n" +
                "    </table_account>\n" +
                "  </body>\n" +
                "</content>";
        
        return response;
    }
}
