package com.psbc.mock.controller;

import com.psbc.mock.monitor.MetricsCollector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控控制器
 * 提供系统监控和指标查询接口
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
public class MonitorController {

    @Autowired
    private MetricsCollector metricsCollector;

    /**
     * 获取系统总体指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<MetricsCollector.SystemMetrics> getSystemMetrics() {
        MetricsCollector.SystemMetrics metrics = metricsCollector.getSystemMetrics();
        return ResponseEntity.ok(metrics);
    }

    /**
     * 获取指定服务的指标
     */
    @GetMapping("/metrics/{serviceName}")
    public ResponseEntity<MetricsCollector.ServiceMetrics> getServiceMetrics(@PathVariable String serviceName) {
        MetricsCollector.ServiceMetrics metrics = metricsCollector.getServiceMetrics(serviceName);
        return ResponseEntity.ok(metrics);
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 基本健康检查
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            
            // 系统指标
            MetricsCollector.SystemMetrics systemMetrics = metricsCollector.getSystemMetrics();
            health.put("totalRequests", systemMetrics.getTotalRequests());
            health.put("successRate", systemMetrics.getSuccessRate());
            health.put("activeConnections", systemMetrics.getActiveConnections());
            health.put("uptime", systemMetrics.getUptime());
            
            // JVM信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            Map<String, Object> memory = new HashMap<>();
            memory.put("heapUsed", memoryBean.getHeapMemoryUsage().getUsed());
            memory.put("heapMax", memoryBean.getHeapMemoryUsage().getMax());
            memory.put("heapCommitted", memoryBean.getHeapMemoryUsage().getCommitted());
            memory.put("nonHeapUsed", memoryBean.getNonHeapMemoryUsage().getUsed());
            health.put("memory", memory);
            
            // 运行时信息
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            Map<String, Object> runtime = new HashMap<>();
            runtime.put("jvmName", runtimeBean.getVmName());
            runtime.put("jvmVersion", runtimeBean.getVmVersion());
            runtime.put("jvmUptime", runtimeBean.getUptime());
            runtime.put("processors", Runtime.getRuntime().availableProcessors());
            health.put("runtime", runtime);
            
            // 服务状态检查
            Map<String, Object> services = new HashMap<>();
            services.put("zhbg", checkServiceHealth("ZHBG"));
            services.put("webservice", checkServiceHealth("WEBSERVICE"));
            services.put("socket", checkServiceHealth("SOCKET"));
            health.put("services", services);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ResponseEntity.status(500).body(health);
        }
    }

    /**
     * 获取详细的系统信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // 应用信息
        Map<String, Object> app = new HashMap<>();
        app.put("name", "Mock Service");
        app.put("version", "1.0.0");
        app.put("description", "挡板测试服务");
        info.put("app", app);
        
        // 系统指标
        info.put("metrics", metricsCollector.getSystemMetrics());
        
        // 服务指标
        Map<String, Object> services = new HashMap<>();
        services.put("zhbg", metricsCollector.getServiceMetrics("ZHBG"));
        services.put("webservice", metricsCollector.getServiceMetrics("WEBSERVICE"));
        services.put("socket", metricsCollector.getServiceMetrics("SOCKET"));
        info.put("services", services);
        
        // 系统属性
        Map<String, Object> system = new HashMap<>();
        system.put("osName", System.getProperty("os.name"));
        system.put("osVersion", System.getProperty("os.version"));
        system.put("osArch", System.getProperty("os.arch"));
        system.put("javaVersion", System.getProperty("java.version"));
        system.put("javaVendor", System.getProperty("java.vendor"));
        info.put("system", system);
        
        return ResponseEntity.ok(info);
    }

    /**
     * 重置指标
     */
    @PostMapping("/metrics/reset")
    public ResponseEntity<String> resetMetrics() {
        try {
            metricsCollector.resetMetrics();
            log.info("指标已重置");
            return ResponseEntity.ok("{\"message\":\"指标已重置\"}");
        } catch (Exception e) {
            log.error("重置指标失败", e);
            return ResponseEntity.status(500).body("{\"error\":\"重置指标失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取实时指标（简化版）
     */
    @GetMapping("/metrics/realtime")
    public ResponseEntity<Map<String, Object>> getRealtimeMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        MetricsCollector.SystemMetrics systemMetrics = metricsCollector.getSystemMetrics();
        metrics.put("totalRequests", systemMetrics.getTotalRequests());
        metrics.put("totalSuccess", systemMetrics.getTotalSuccess());
        metrics.put("totalErrors", systemMetrics.getTotalErrors());
        metrics.put("successRate", systemMetrics.getSuccessRate());
        metrics.put("averageResponseTime", systemMetrics.getAverageResponseTime());
        metrics.put("activeConnections", systemMetrics.getActiveConnections());
        metrics.put("timestamp", System.currentTimeMillis());
        
        // 内存使用情况
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        long heapMax = memoryBean.getHeapMemoryUsage().getMax();
        metrics.put("memoryUsage", (double) heapUsed / heapMax * 100);
        
        return ResponseEntity.ok(metrics);
    }

    /**
     * 获取错误统计
     */
    @GetMapping("/errors")
    public ResponseEntity<Map<String, Object>> getErrorStatistics() {
        Map<String, Object> errors = new HashMap<>();
        
        // 获取各服务的错误统计
        MetricsCollector.ServiceMetrics zhbgMetrics = metricsCollector.getServiceMetrics("ZHBG");
        MetricsCollector.ServiceMetrics wsMetrics = metricsCollector.getServiceMetrics("WEBSERVICE");
        MetricsCollector.ServiceMetrics socketMetrics = metricsCollector.getServiceMetrics("SOCKET");
        
        errors.put("zhbg", Map.of(
            "totalErrors", zhbgMetrics.getTotalErrors(),
            "errorRate", 100 - zhbgMetrics.getSuccessRate()
        ));
        
        errors.put("webservice", Map.of(
            "totalErrors", wsMetrics.getTotalErrors(),
            "errorRate", 100 - wsMetrics.getSuccessRate()
        ));
        
        errors.put("socket", Map.of(
            "totalErrors", socketMetrics.getTotalErrors(),
            "errorRate", 100 - socketMetrics.getSuccessRate()
        ));
        
        // 总体错误统计
        MetricsCollector.SystemMetrics systemMetrics = metricsCollector.getSystemMetrics();
        errors.put("total", Map.of(
            "totalErrors", systemMetrics.getTotalErrors(),
            "errorRate", 100 - systemMetrics.getSuccessRate()
        ));
        
        return ResponseEntity.ok(errors);
    }

    /**
     * 获取性能统计
     */
    @GetMapping("/performance")
    public ResponseEntity<Map<String, Object>> getPerformanceStatistics() {
        Map<String, Object> performance = new HashMap<>();
        
        MetricsCollector.SystemMetrics systemMetrics = metricsCollector.getSystemMetrics();
        
        // 基本性能指标
        performance.put("totalRequests", systemMetrics.getTotalRequests());
        performance.put("averageResponseTime", systemMetrics.getAverageResponseTime());
        performance.put("uptime", systemMetrics.getUptime());
        
        // 计算TPS（每秒事务数）
        double tps = systemMetrics.getUptime() > 0 ? 
            (double) systemMetrics.getTotalRequests() / systemMetrics.getUptime() : 0;
        performance.put("tps", tps);
        
        // 各服务性能
        Map<String, Object> services = new HashMap<>();
        
        MetricsCollector.ServiceMetrics zhbgMetrics = metricsCollector.getServiceMetrics("ZHBG");
        services.put("zhbg", Map.of(
            "requests", zhbgMetrics.getTotalRequests(),
            "averageResponseTime", zhbgMetrics.getAverageResponseTime()
        ));
        
        MetricsCollector.ServiceMetrics wsMetrics = metricsCollector.getServiceMetrics("WEBSERVICE");
        services.put("webservice", Map.of(
            "requests", wsMetrics.getTotalRequests(),
            "averageResponseTime", wsMetrics.getAverageResponseTime()
        ));
        
        MetricsCollector.ServiceMetrics socketMetrics = metricsCollector.getServiceMetrics("SOCKET");
        services.put("socket", Map.of(
            "requests", socketMetrics.getTotalRequests(),
            "averageResponseTime", socketMetrics.getAverageResponseTime()
        ));
        
        performance.put("services", services);
        
        return ResponseEntity.ok(performance);
    }

    /**
     * 检查服务健康状态
     */
    private Map<String, Object> checkServiceHealth(String serviceName) {
        Map<String, Object> serviceHealth = new HashMap<>();
        
        MetricsCollector.ServiceMetrics metrics = metricsCollector.getServiceMetrics(serviceName);
        
        // 基于成功率判断健康状态
        double successRate = metrics.getSuccessRate();
        String status;
        if (successRate >= 95) {
            status = "HEALTHY";
        } else if (successRate >= 80) {
            status = "WARNING";
        } else {
            status = "UNHEALTHY";
        }
        
        serviceHealth.put("status", status);
        serviceHealth.put("successRate", successRate);
        serviceHealth.put("totalRequests", metrics.getTotalRequests());
        serviceHealth.put("averageResponseTime", metrics.getAverageResponseTime());
        
        return serviceHealth;
    }
}
