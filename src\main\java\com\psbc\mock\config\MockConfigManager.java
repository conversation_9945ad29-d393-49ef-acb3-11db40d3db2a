package com.psbc.mock.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 挡板配置管理器
 * 支持动态修改响应内容和行为配置
 */
@Slf4j
@Component
public class MockConfigManager {

    // 响应模板配置
    private final Map<String, ResponseTemplate> responseTemplates = new ConcurrentHashMap<>();
    
    // 服务配置
    private final Map<String, ServiceConfig> serviceConfigs = new ConcurrentHashMap<>();
    
    public MockConfigManager() {
        initDefaultConfigs();
    }
    
    /**
     * 初始化默认配置
     */
    private void initDefaultConfigs() {
        // 初始化默认响应模板
        initDefaultResponseTemplates();
        
        // 初始化默认服务配置
        initDefaultServiceConfigs();
    }
    
    /**
     * 初始化默认响应模板
     */
    private void initDefaultResponseTemplates() {
        // 30001接口成功响应模板
        ResponseTemplate template30001 = new ResponseTemplate();
        template30001.setServiceNo("30001");
        template30001.setType("SUCCESS");
        template30001.setTemplate("{\n" +
                "  \"status\": \"success\",\n" +
                "  \"code\": \"0000\",\n" +
                "  \"message\": \"查询成功\",\n" +
                "  \"data\": {\n" +
                "    \"loanInfo\": {\n" +
                "      \"loanId\": \"LOAN${timestamp}\",\n" +
                "      \"amount\": \"${randomAmount}\",\n" +
                "      \"status\": \"APPROVED\",\n" +
                "      \"approveDate\": \"${currentDate}\",\n" +
                "      \"customerName\": \"测试客户\",\n" +
                "      \"idCard\": \"350200199001010001\",\n" +
                "      \"bankAccount\": \"6217000010001234567\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"timestamp\": \"${timestamp}\"\n" +
                "}");
        responseTemplates.put("30001_SUCCESS", template30001);
        
        // 20006接口成功响应模板
        ResponseTemplate template20006 = new ResponseTemplate();
        template20006.setServiceNo("20006");
        template20006.setType("SUCCESS");
        template20006.setTemplate("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>查询成功</msg>\n" +
                "    <timestamp>${timestamp}</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <accountInfo>\n" +
                "      <accountNo>6217000010001234567</accountNo>\n" +
                "      <ismatch>0</ismatch>\n" +
                "      <balance>${randomBalance}</balance>\n" +
                "      <status>ACTIVE</status>\n" +
                "      <lastUpdateTime>${currentDateTime}</lastUpdateTime>\n" +
                "      <customerName>测试客户</customerName>\n" +
                "      <idCard>350200199001010001</idCard>\n" +
                "    </accountInfo>\n" +
                "  </body>\n" +
                "</response>");
        responseTemplates.put("20006_SUCCESS", template20006);
        
        // WebService成功响应模板
        ResponseTemplate templateWS = new ResponseTemplate();
        templateWS.setServiceNo("WEBSERVICE");
        templateWS.setType("SUCCESS");
        templateWS.setTemplate("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>交易成功</msg>\n" +
                "    <timestamp>${timestamp}</timestamp>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <r>\n" +
                "      <transactionId>TXN${timestamp}</transactionId>\n" +
                "      <status>SUCCESS</status>\n" +
                "      <message>委托方系统处理成功</message>\n" +
                "      <processTime>${currentDateTime}</processTime>\n" +
                "      <bankId>${bankId}</bankId>\n" +
                "      <data>\n" +
                "        <referenceNo>REF${timestamp}</referenceNo>\n" +
                "        <amount>${randomAmount}</amount>\n" +
                "        <description>委托方业务处理</description>\n" +
                "      </data>\n" +
                "    </r>\n" +
                "  </body>\n" +
                "</content>");
        responseTemplates.put("WEBSERVICE_SUCCESS", templateWS);
        
        // Socket成功响应模板
        ResponseTemplate templateSocket = new ResponseTemplate();
        templateSocket.setServiceNo("SOCKET");
        templateSocket.setType("SUCCESS");
        templateSocket.setTemplate("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<content>\n" +
                "  <head>\n" +
                "    <statecode>1</statecode>\n" +
                "    <msg>交易成功</msg>\n" +
                "    <timestamp>${timestamp}</timestamp>\n" +
                "    <serviceno>${serviceno}</serviceno>\n" +
                "    <messageId>${messageId}</messageId>\n" +
                "  </head>\n" +
                "  <body>\n" +
                "    <table_account>\n" +
                "      <row>\n" +
                "        <instructionno>MOCK${timestamp}</instructionno>\n" +
                "        <issuccess>1</issuccess>\n" +
                "        <amount>${randomAmount}</amount>\n" +
                "        <balance>${randomBalance}</balance>\n" +
                "        <transactionTime>${timestamp}</transactionTime>\n" +
                "        <messageId>${messageId}</messageId>\n" +
                "      </row>\n" +
                "    </table_account>\n" +
                "  </body>\n" +
                "</content>");
        responseTemplates.put("SOCKET_SUCCESS", templateSocket);
    }
    
    /**
     * 初始化默认服务配置
     */
    private void initDefaultServiceConfigs() {
        // 综合办公系统配置
        ServiceConfig zhbgConfig = new ServiceConfig();
        zhbgConfig.setServiceName("ZHBG");
        zhbgConfig.setEnabled(true);
        zhbgConfig.setResponseDelay(100);
        zhbgConfig.setErrorRate(0);
        serviceConfigs.put("ZHBG", zhbgConfig);
        
        // WebService配置
        ServiceConfig wsConfig = new ServiceConfig();
        wsConfig.setServiceName("WEBSERVICE");
        wsConfig.setEnabled(true);
        wsConfig.setResponseDelay(200);
        wsConfig.setErrorRate(0);
        serviceConfigs.put("WEBSERVICE", wsConfig);
        
        // Socket配置
        ServiceConfig socketConfig = new ServiceConfig();
        socketConfig.setServiceName("SOCKET");
        socketConfig.setEnabled(true);
        socketConfig.setResponseDelay(50);
        socketConfig.setErrorRate(0);
        serviceConfigs.put("SOCKET", socketConfig);
    }
    
    /**
     * 获取响应模板
     */
    public ResponseTemplate getResponseTemplate(String serviceNo, String type) {
        String key = serviceNo + "_" + type;
        return responseTemplates.get(key);
    }
    
    /**
     * 设置响应模板
     */
    public void setResponseTemplate(String serviceNo, String type, String template) {
        String key = serviceNo + "_" + type;
        ResponseTemplate responseTemplate = responseTemplates.get(key);
        if (responseTemplate == null) {
            responseTemplate = new ResponseTemplate();
            responseTemplate.setServiceNo(serviceNo);
            responseTemplate.setType(type);
            responseTemplates.put(key, responseTemplate);
        }
        responseTemplate.setTemplate(template);
        log.info("更新响应模板: {} = {}", key, template);
    }
    
    /**
     * 获取服务配置
     */
    public ServiceConfig getServiceConfig(String serviceName) {
        return serviceConfigs.get(serviceName);
    }
    
    /**
     * 设置服务配置
     */
    public void setServiceConfig(String serviceName, ServiceConfig config) {
        serviceConfigs.put(serviceName, config);
        log.info("更新服务配置: {} = {}", serviceName, config);
    }
    
    /**
     * 获取所有响应模板
     */
    public Map<String, ResponseTemplate> getAllResponseTemplates() {
        return new ConcurrentHashMap<>(responseTemplates);
    }
    
    /**
     * 获取所有服务配置
     */
    public Map<String, ServiceConfig> getAllServiceConfigs() {
        return new ConcurrentHashMap<>(serviceConfigs);
    }
    
    /**
     * 响应模板
     */
    @Data
    public static class ResponseTemplate {
        private String serviceNo;
        private String type;
        private String template;
        private String description;
    }
    
    /**
     * 服务配置
     */
    @Data
    public static class ServiceConfig {
        private String serviceName;
        private boolean enabled;
        private int responseDelay;
        private int errorRate;
        private String description;
    }
}
