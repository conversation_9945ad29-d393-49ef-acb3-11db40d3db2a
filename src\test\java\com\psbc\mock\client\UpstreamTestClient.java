package com.psbc.mock.client;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 上游客户端测试工具
 * 模拟真实的上游系统，向xmysfzjjg项目发送各种测试请求
 * 用于测试整个链路的连通性
 */
@Slf4j
public class UpstreamTestClient {

    // xmysfzjjg项目的Socket服务端口（根据配置文件中的WG_PORT）
    private static final String XMYSFZJJG_HOST = "localhost";
    private static final int XMYSFZJJG_PORT = 8888;
    
    public static void main(String[] args) {
        UpstreamTestClient client = new UpstreamTestClient();
        
        log.info("开始测试上游客户端向xmysfzjjg发送请求...");
        
        // 等待服务启动
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试各种业务场景
        client.testLoanQuery();           // 测试按揭贷款查询
        client.testAccountQuery();        // 测试监管账户查询
        client.testTransactionProcess();  // 测试交易处理
        client.testErrorScenario();       // 测试错误场景
        client.testTimeoutScenario();     // 测试超时场景
        client.testConcurrentRequests();  // 测试并发请求
        
        log.info("上游客户端测试完成");
    }

    /**
     * 测试按揭贷款查询（服务号30001）
     */
    public void testLoanQuery() {
        try {
            log.info("测试按揭贷款查询（服务号30001）...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>30001</serviceno>\n" +
                    "    <reqSysNo>*********</reqSysNo>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <customerInfo>\n" +
                    "      <customerName>张三</customerName>\n" +
                    "      <idCard>350200199001010001</idCard>\n" +
                    "      <phoneNumber>13800138000</phoneNumber>\n" +
                    "    </customerInfo>\n" +
                    "    <loanRequest>\n" +
                    "      <amount>500000.00</amount>\n" +
                    "      <term>360</term>\n" +
                    "      <purpose>购房</purpose>\n" +
                    "    </loanRequest>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            log.info("按揭贷款查询响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试按揭贷款查询失败", e);
        }
    }

    /**
     * 测试监管账户查询（服务号20006）
     */
    public void testAccountQuery() {
        try {
            log.info("测试监管账户查询（服务号20006）...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>20006</serviceno>\n" +
                    "    <reqSysNo>*********</reqSysNo>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <accountQuery>\n" +
                    "      <accountNo>6217000010001234567</accountNo>\n" +
                    "      <queryType>BALANCE</queryType>\n" +
                    "      <startDate>2024-01-01</startDate>\n" +
                    "      <endDate>2024-12-31</endDate>\n" +
                    "    </accountQuery>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            log.info("监管账户查询响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试监管账户查询失败", e);
        }
    }

    /**
     * 测试交易处理（默认服务号）
     */
    public void testTransactionProcess() {
        try {
            log.info("测试交易处理（默认服务号）...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>99999</serviceno>\n" +
                    "    <reqSysNo>*********</reqSysNo>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <transaction>\n" +
                    "      <transactionType>TRANSFER</transactionType>\n" +
                    "      <fromAccount>6217000010001234567</fromAccount>\n" +
                    "      <toAccount>6217000010001234568</toAccount>\n" +
                    "      <amount>" + ThreadLocalRandom.current().nextInt(1000, 50000) + ".00</amount>\n" +
                    "      <currency>CNY</currency>\n" +
                    "      <description>测试转账</description>\n" +
                    "    </transaction>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            log.info("交易处理响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试交易处理失败", e);
        }
    }

    /**
     * 测试错误场景
     */
    public void testErrorScenario() {
        try {
            log.info("测试错误场景...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>30001</serviceno>\n" +
                    "    <reqSysNo>*********</reqSysNo>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <testData>error</testData>\n" +
                    "    <description>测试错误响应</description>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            log.info("错误场景响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试错误场景失败", e);
        }
    }

    /**
     * 测试超时场景
     */
    public void testTimeoutScenario() {
        try {
            log.info("测试超时场景...");
            
            String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <serviceno>20006</serviceno>\n" +
                    "    <reqSysNo>*********</reqSysNo>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <testData>timeout</testData>\n" +
                    "    <description>测试超时响应</description>\n" +
                    "  </body>\n" +
                    "</content>";
            
            String response = sendSocketMessage(message);
            log.info("超时场景响应: {}", response);
            
        } catch (Exception e) {
            log.error("测试超时场景失败", e);
        }
    }

    /**
     * 测试并发请求
     */
    public void testConcurrentRequests() {
        log.info("测试并发请求...");
        
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                try {
                    String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                            "<content>\n" +
                            "  <head>\n" +
                            "    <serviceno>99999</serviceno>\n" +
                            "    <reqSysNo>*********</reqSysNo>\n" +
                            "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                            "  </head>\n" +
                            "  <body>\n" +
                            "    <threadId>" + threadId + "</threadId>\n" +
                            "    <description>并发测试请求</description>\n" +
                            "  </body>\n" +
                            "</content>";
                    
                    String response = sendSocketMessage(message);
                    log.info("并发请求 [线程{}] 响应: {}", threadId, response);
                    
                } catch (Exception e) {
                    log.error("并发请求 [线程{}] 失败", threadId, e);
                }
            });
            threads[i].start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("并发请求测试完成");
    }

    /**
     * 发送Socket消息到xmysfzjjg项目
     */
    private String sendSocketMessage(String message) throws Exception {
        try (Socket socket = new Socket(XMYSFZJJG_HOST, XMYSFZJJG_PORT)) {
            // 发送消息
            sendFormattedMessage(socket, message);
            
            // 接收响应
            return receiveFormattedMessage(socket);
        }
    }

    /**
     * 发送格式化的消息（添加6位长度前缀）
     */
    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        // 格式化长度字段为6位字符串
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        try (OutputStream out = socket.getOutputStream()) {
            // 发送长度字段
            out.write(lengthBytes);
            // 发送消息体
            out.write(messageBytes);
            out.flush();
        }
    }

    /**
     * 接收格式化的消息（解析6位长度前缀）
     */
    private String receiveFormattedMessage(Socket socket) throws IOException {
        try (InputStream in = socket.getInputStream()) {
            // 读取长度字段（6字节）
            byte[] lengthBytes = new byte[6];
            int bytesRead = in.read(lengthBytes);
            if (bytesRead != 6) {
                throw new IOException("无法读取完整的长度字段");
            }
            
            String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
            int messageLength = Integer.parseInt(lengthStr);
            
            // 读取消息体
            byte[] messageBytes = new byte[messageLength];
            int totalBytesRead = 0;
            while (totalBytesRead < messageLength) {
                int currentBytesRead = in.read(messageBytes, totalBytesRead, messageLength - totalBytesRead);
                if (currentBytesRead == -1) {
                    throw new IOException("连接意外关闭");
                }
                totalBytesRead += currentBytesRead;
            }
            
            return new String(messageBytes, StandardCharsets.UTF_8);
        }
    }
}
