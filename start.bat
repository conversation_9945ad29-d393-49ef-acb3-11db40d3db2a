@echo off
echo ========================================
echo 启动挡板测试服务
echo ========================================

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装JDK 8或更高版本
    pause
    exit /b 1
)

echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo 启动挡板测试服务...
echo 服务将在以下端口启动：
echo - 综合办公系统HTTP服务: 9091
echo - 委托方WebService服务: 9091/xmysfzjjg/wservices/IWebServiceService?wsdl
echo - 外联系统Socket服务: 9702
echo ========================================

mvn spring-boot:run

pause
