package com.psbc.mock.config;

import com.psbc.mock.service.MockWebService;
import com.psbc.mock.service.impl.MockWebServiceImpl;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.ws.Endpoint;

/**
 * WebService配置类
 * 配置CXF WebService服务
 */
@Configuration
public class WebServiceConfig {

    /**
     * 配置CXF Servlet
     */
    @Bean
    public ServletRegistrationBean<CXFServlet> cxfServlet() {
        return new ServletRegistrationBean<>(new CXFServlet(), "/xmysfzjjg/wservices/*");
    }

    /**
     * 配置Spring Bus
     */
    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }

    /**
     * 配置WebService实现类
     */
    @Bean
    public MockWebService mockWebService() {
        return new MockWebServiceImpl();
    }

    /**
     * 发布WebService端点
     */
    @Bean
    public Endpoint endpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), mockWebService());
        endpoint.publish("/IWebServiceService");
        return endpoint;
    }
}
