<?xml version="1.0" encoding="UTF-8"?>  
<!-- START SNIPPET: beans -->  
<beans xmlns="http://www.springframework.org/schema/beans"  
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
    xmlns:jaxws="http://cxf.apache.org/jaxws"  
    xsi:schemaLocation="  
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd  
http://cxf.apache.org/jaxws http://cxf.apache.org/schema/jaxws.xsd">  

    <bean id="client" class="com.psbc.webservice.ClientIWebService"   
      factory-bean="clientFactory" factory-method="create"/>  
    <bean id="clientFactory" class="org.apache.cxf.jaxws.JaxWsProxyFactoryBean">  
		<property name="serviceClass" value="com.psbc.webservice.ClientIWebService"/>  
		<!-- 本地环境 -->
		<!-- <property name="address" value="https://127.0.0.1:8443/xmysfzjjg/wservices/IWebServiceService?wsdl"/>    -->
		<!-- 测试环境 -->
		<!--  <property name="address" value="https://************:8088/JYDJService/JYDJService1.asmx?wsdl"/> -->
	        <!-- 生产环境 -->
		<property name="address" value="https://*************:8088/JYDJService/JYDJService1.asmx?wsdl"/>
		
		
    </bean>  
</beans> 
