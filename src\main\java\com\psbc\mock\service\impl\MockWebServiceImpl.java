package com.psbc.mock.service.impl;

import com.psbc.mock.service.MockWebService;
import lombok.extern.slf4j.Slf4j;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * 委托方WebService实现类
 * 模拟委托方系统的WebService响应
 */
@Slf4j
@WebService(
    serviceName = "IWebServiceService",
    targetNamespace = "http://tempuri.org/",
    endpointInterface = "com.psbc.mock.service.MockWebService"
)
public class MockWebServiceImpl implements MockWebService {

    @Override
    @WebMethod(operationName = "Execute")
    @WebResult(name = "ExecuteResult", targetNamespace = "http://tempuri.org/")
    public String Execute(
            @WebParam(targetNamespace = "http://tempuri.org/", name = "BankID") String bankId,
            @WebParam(targetNamespace = "http://tempuri.org/", name = "inParmeter") String inParmeter) {
        
        log.info("收到委托方WebService请求 - BankID: {}, inParmeter: {}", bankId, inParmeter);
        
        // 模拟处理时间
        try {
            Thread.sleep(100); // 模拟100ms处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 根据请求内容返回不同的响应
        String response;
        if (inParmeter != null && inParmeter.contains("serviceno")) {
            // 如果包含serviceno，返回成功响应
            response = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <statecode>1</statecode>\n" +
                    "    <msg>交易成功</msg>\n" +
                    "    <timestamp>" + System.currentTimeMillis() + "</timestamp>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <result>\n" +
                    "      <transactionId>TXN" + System.currentTimeMillis() + "</transactionId>\n" +
                    "      <status>SUCCESS</status>\n" +
                    "      <message>委托方系统处理成功</message>\n" +
                    "      <data>\n" +
                    "        <bankId>" + bankId + "</bankId>\n" +
                    "        <processTime>" + System.currentTimeMillis() + "</processTime>\n" +
                    "      </data>\n" +
                    "    </result>\n" +
                    "  </body>\n" +
                    "</content>";
        } else {
            // 默认响应
            response = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<content>\n" +
                    "  <head>\n" +
                    "    <statecode>1</statecode>\n" +
                    "    <msg>默认响应</msg>\n" +
                    "  </head>\n" +
                    "  <body>\n" +
                    "    <message>委托方WebService挡板响应</message>\n" +
                    "  </body>\n" +
                    "</content>";
        }
        
        log.info("返回委托方WebService响应: {}", response);
        return response;
    }
}
