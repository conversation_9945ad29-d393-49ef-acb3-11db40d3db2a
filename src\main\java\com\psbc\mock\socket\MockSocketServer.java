package com.psbc.mock.socket;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 外联系统Socket服务器模拟
 * 模拟外联系统的Socket通信
 */
@Slf4j
@Component
public class MockSocketServer {

    @Value("${mock.socket.port:9702}")
    private int port;

    @Value("${mock.socket.enabled:true}")
    private boolean enabled;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture channelFuture;

    @PostConstruct
    public void init() {
        if (enabled) {
            new Thread(this::start, "mock-socket-server").start();
        }
    }

    public void start() {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap bootstrap = new ServerBootstrap()
                    .group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) {
                            ch.pipeline()
                                    .addLast(new MockSocketDecoder())
                                    .addLast(new MockSocketEncoder())
                                    .addLast(new MockSocketHandler());
                        }
                    });

            channelFuture = bootstrap.bind(port).sync();
            log.info("外联系统Socket服务器启动成功，监听端口: {}", port);

            // 等待服务器关闭
            channelFuture.channel().closeFuture().sync();

        } catch (Exception e) {
            log.error("外联系统Socket服务器启动失败", e);
        } finally {
            stopServer();
        }
    }

    @PreDestroy
    public void stopServer() {
        if (channelFuture != null) {
            channelFuture.channel().close();
        }
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        log.info("外联系统Socket服务器已停止");
    }
}
