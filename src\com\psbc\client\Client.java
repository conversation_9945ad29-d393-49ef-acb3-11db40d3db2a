package com.psbc.client;

import com.psbc.webservice.ClientIWebService;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.transport.http.HTTPConduit;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public final class Client {
  public static void main(String[] args) throws Exception {
    ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(new String[] { "client-beans.xml" });
    X509TrustManager tm = new X509TrustManager() {
        public X509Certificate[] getAcceptedIssuers() {
          return null;
        }
        
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
        
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
      };
    TLSClientParameters tls = new TLSClientParameters();
    ClientIWebService client = (ClientIWebService)context.getBean("client");
    org.apache.cxf.endpoint.Client proxy = ClientProxy.getClient(client);
    HTTPConduit conduit = (HTTPConduit)proxy.getConduit();
    tls.setTrustManagers(new TrustManager[] { tm });
    tls.setDisableCNCheck(true);
    conduit.setTlsClientParameters(tls);
  }
  
  
}
