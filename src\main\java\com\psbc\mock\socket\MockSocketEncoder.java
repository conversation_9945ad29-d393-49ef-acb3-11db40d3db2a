package com.psbc.mock.socket;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 外联系统Socket消息编码器
 * 为响应消息添加6位长度前缀
 */
@Slf4j
public class MockSocketEncoder extends MessageToByteEncoder<String> {

    private static final int LENGTH_FIELD_SIZE = 6;

    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) throws Exception {
        if (msg == null) {
            return;
        }

        // 将消息转换为字节数组
        byte[] messageBytes = msg.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;

        // 格式化长度字段为6位字符串（左补0）
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);

        log.info("外联系统发送响应，长度: {}, 内容: {}", messageLength, msg);

        // 写入长度字段
        out.writeBytes(lengthBytes);
        // 写入消息体
        out.writeBytes(messageBytes);
    }
}
