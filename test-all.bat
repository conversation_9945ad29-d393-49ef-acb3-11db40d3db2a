@echo off
echo ========================================
echo Mock-Work 挡板服务完整测试脚本
echo ========================================
echo.

echo 1. 启动挡板服务...
echo 请在另一个终端运行: mvn spring-boot:run
echo 等待服务启动完成后按任意键继续...
pause > nul
echo.

echo 2. 测试健康检查...
curl -s http://localhost:9091/api/admin/xzp/health
echo.
echo.

echo 3. 测试30001接口...
curl -X POST http://localhost:9091/api/admin/xzp/queryYxjfLsxdye ^
  -H "Content-Type: application/json" ^
  -d "{\"serviceno\":\"30001\",\"data\":\"test\"}"
echo.
echo.

echo 4. 测试20006接口...
curl -X POST http://localhost:9091/api/admin/xzp/queryJgzhInfo ^
  -H "Content-Type: application/xml" ^
  -d "<?xml version=\"1.0\"?><content><head><serviceno>20006</serviceno></head></content>"
echo.
echo.

echo 5. 测试配置管理...
echo 设置响应延迟为200ms:
curl -X POST http://localhost:9091/api/config/delay/200
echo.
echo 获取服务配置:
curl -s http://localhost:9091/api/config/services
echo.
echo.

echo 6. 测试监控指标...
echo 获取系统指标:
curl -s http://localhost:9091/api/monitor/metrics
echo.
echo 获取实时指标:
curl -s http://localhost:9091/api/monitor/metrics/realtime
echo.
echo.

echo 7. 运行上游客户端测试...
echo 请在另一个终端运行以下命令:
echo mvn test-compile exec:java -Dexec.mainClass="com.psbc.mock.client.UpstreamTestClient"
echo.

echo 8. 运行集成测试...
echo 请在另一个终端运行以下命令:
echo mvn test -Dtest=FullFlowIntegrationTest
echo.

echo 9. 重置配置...
curl -X POST http://localhost:9091/api/config/reset
echo.
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如需查看详细监控信息，请访问:
echo - 系统指标: http://localhost:9091/api/monitor/info
echo - 配置帮助: http://localhost:9091/api/config/help
echo - 服务状态: http://localhost:9091/api/admin/xzp/status
echo.
pause
